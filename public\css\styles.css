:root {
  --primary-color: #4285f4;
  --primary-dark: #3367d6;
  --secondary-color: #34a853;
  --error-color: #ea4335;
  --warning-color: #fbbc05;
  --text-primary: #202124;
  --text-secondary: #5f6368;
  --background-light: #f5f7fa;
  --background-dark: #c3cfe2;
  --card-background: #ffffff;
  --border-color: #dadce0;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --critical-color: #ea4335;
  --normal-color: #34a853;
  --borderline-color: #fbbc05;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  background: linear-gradient(135deg, var(--background-light) 0%, var(--background-dark) 100%);
  min-height: 100vh;
  color: var(--text-primary);
}

.container {
  width: 100%;
  max-width: 800px;
  margin: 60px auto 0;
  padding: 20px;
}

.card {
  background-color: var(--card-background);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 5px 15px var(--shadow-color);
  margin-bottom: 20px;
}
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.app-title {
  font-size: 1.25rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  margin-right: auto;
}

.user-info {
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 6px;
  border-radius: 24px;
  transition: all 0.3s ease;
}

.user-info:hover {
  background: rgba(255, 255, 255, 0.1);
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: transform 0.3s ease;
}

.user-info:hover .avatar {
  transform: scale(1.05);
}

.user-menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  background-color: var(--card-background);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 200px;
  z-index: 1000;
  display: none;
  overflow: hidden;
  animation: menuSlideDown 0.2s ease;
}

.user-menu.active {
  display: block;
}

@keyframes menuSlideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-menu-item {
  padding: 14px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  color: var(--text-primary);
  transition: all 0.2s ease;
  font-size: 0.95rem;
}

.user-menu-item i {
  font-size: 1.1rem;
  color: var(--text-secondary);
  width: 20px;
  text-align: center;
  transition: color 0.2s ease;
}

.user-menu-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.user-menu-item:hover i {
  color: var(--primary-color);
}

#logout-btn {
  border-top: 1px solid var(--border-color);
  color: var(--error-color);
}

#logout-btn:hover {
  background-color: rgba(234, 67, 53, 0.08);
}

#logout-btn i {
  color: var(--error-color);
}

/* 底部导航 */
.bottom-nav {
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--card-background);
  box-shadow: 0 -2px 5px var(--shadow-color);
  padding: 10px 0;
  z-index: 100;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: var(--text-secondary);
  font-size: 0.8rem;
}

.nav-item.active {
  color: var(--primary-color);
}

.nav-icon {
  font-size: 1.5rem;
  margin-bottom: 5px;
}

/* 按钮样式 */
.btn {
  padding: 10px 15px;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-error {
  background-color: var(--error-color);
  color: white;
}

/* 表单样式 */
.form-group {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: var(--text-secondary);
}

.form-input {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 1rem;
}

/* 报告列表样式 */
.report-list {
  list-style: none;
}

.report-item {
  padding: 15px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.report-date {
  font-weight: 500;
}

.report-score {
  font-weight: bold;
  padding: 5px 10px;
  border-radius: 20px;
  color: white;
}

.score-good {
  background-color: var(--normal-color);
}

.score-warning {
  background-color: var(--warning-color);
}

.score-bad {
  background-color: var(--error-color);
}

/* 指标样式 */
.indicator {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid var(--border-color);
}

.indicator-name {
  font-weight: 500;
}

.indicator-value {
  font-weight: bold;
}

.indicator-normal {
  color: var(--normal-color);
}

.indicator-warning {
  color: var(--borderline-color);
}

.indicator-critical {
  color: var(--critical-color);
}

/* 图表容器 */
.chart-container {
  width: 100%;
  height: 300px;
  margin: 20px 0;
}

/* 时间轴 */
.timeline {
  position: relative;
  margin: 20px 0;
  padding-left: 30px;
}

.timeline-item {
  position: relative;
  padding-bottom: 20px;
}

.timeline-item:before {
  content: '';
  position: absolute;
  left: -30px;
  top: 0;
  width: 2px;
  height: 100%;
  background-color: var(--border-color);
}

.timeline-item:after {
  content: '';
  position: absolute;
  left: -36px;
  top: 0;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: var(--primary-color);
}

/* 加载动画 */
.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 20px auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .container {
    padding: 15px;
  }
  
  .card {
    padding: 15px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: #e8eaed;
    --text-secondary: #9aa0a6;
    --card-background: #202124;
    --border-color: #5f6368;
    --background-light: #202124;
    --background-dark: #303134;
  }
}

/* 健康评分卡片样式 */
.health-score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.score-circle {
  position: relative;
  width: 180px;
  height: 180px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4285f4, #34a853);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(66, 133, 244, 0.2);
}

.score-number {
  font-size: 48px;
  font-weight: bold;
  line-height: 1;
}

.score-text {
  font-size: 18px;
  margin-top: 5px;
}

.score-info {
  position: absolute;
  bottom: 10px;
  right: 10px;
  cursor: help;
  background: rgba(128, 128, 128, 0.6);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.score-info:hover {
  background: rgba(128, 128, 128, 0.8);
  transform: scale(1.1);
}

.score-info i {
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.score-tooltip {
  display: none;
  position: absolute;
  bottom: 100%;
  right: -60px;
  background: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  width: 160px;
  color: var(--text-primary);
  z-index: 100;
  margin-bottom: 10px;
  font-size: 14px;
  animation: fadeIn 0.2s ease;
}

.score-tooltip:after {
  content: '';
  position: absolute;
  bottom: -6px;
  right: 70px;
  width: 12px;
  height: 12px;
  background: white;
  transform: rotate(45deg);
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.score-info:hover .score-tooltip {
  display: block;
}

.tooltip-item {
  padding: 6px 0;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

.tooltip-item:last-child {
  border-bottom: none;
}

.score-details {
  display: flex;
  justify-content: space-around;
  width: 100%;
  padding: 0 20px;
}

.score-detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 15px;
  background: var(--background-light);
  border-radius: 10px;
  min-width: 140px;
}

.score-detail-item i {
  font-size: 20px;
  color: var(--primary-color);
}

.detail-content {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.detail-value {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.detail-value.positive {
  color: var(--success-color);
}
/* 健康评分卡片样式 */
.health-score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.score-circle {
  position: relative;
  width: 180px;
  height: 180px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4285f4, #34a853);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(66, 133, 244, 0.2);
}

.score-number {
  font-size: 48px;
  font-weight: bold;
  line-height: 1;
}

.score-text {
  font-size: 18px;
  margin-top: 5px;
}

.score-info {
  position: absolute;
  bottom: 10px;
  right: 10px;
  cursor: help;
  background: rgba(128, 128, 128, 0.6);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.score-info:hover {
  background: rgba(128, 128, 128, 0.8);
  transform: scale(1.1);
}

.score-info i {
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.score-tooltip {
  display: none;
  position: absolute;
  bottom: 100%;
  right: -60px;
  background: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  width: 160px;
  color: var(--text-primary);
  z-index: 100;
  margin-bottom: 10px;
  font-size: 14px;
  animation: fadeIn 0.2s ease;
}

.score-tooltip:after {
  content: '';
  position: absolute;
  bottom: -6px;
  right: 70px;
  width: 12px;
  height: 12px;
  background: white;
  transform: rotate(45deg);
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.score-info:hover .score-tooltip {
  display: block;
}

.tooltip-item {
  padding: 6px 0;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

.tooltip-item:last-child {
  border-bottom: none;
}

.score-details {
  display: flex;
  justify-content: space-around;
  width: 100%;
  padding: 0 20px;
}

.score-detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 15px;
  background: var(--background-light);
  border-radius: 10px;
  min-width: 140px;
}

.score-detail-item i {
  font-size: 20px;
  color: var(--primary-color);
}

.detail-content {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.detail-value {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.detail-value.positive {
  color: var(--success-color);
}
/* 健康评分卡片样式 */
.health-score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.score-circle {
  position: relative;
  width: 180px;
  height: 180px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4285f4, #34a853);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(66, 133, 244, 0.2);
}

.score-number {
  font-size: 48px;
  font-weight: bold;
  line-height: 1;
}

.score-text {
  font-size: 18px;
  margin-top: 5px;
}

.score-info {
  position: absolute;
  bottom: 10px;
  right: 10px;
  cursor: help;
  background: rgba(128, 128, 128, 0.6);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.score-info:hover {
  background: rgba(128, 128, 128, 0.8);
  transform: scale(1.1);
}

.score-info i {
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.score-tooltip {
  display: none;
  position: absolute;
  bottom: 100%;
  right: -60px;
  background: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  width: 160px;
  color: var(--text-primary);
  z-index: 100;
  margin-bottom: 10px;
  font-size: 14px;
  animation: fadeIn 0.2s ease;
}

.score-tooltip:after {
  content: '';
  position: absolute;
  bottom: -6px;
  right: 70px;
  width: 12px;
  height: 12px;
  background: white;
  transform: rotate(45deg);
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.score-info:hover .score-tooltip {
  display: block;
}

.tooltip-item {
  padding: 6px 0;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

.tooltip-item:last-child {
  border-bottom: none;
}

.score-details {
  display: flex;
  justify-content: space-around;
  width: 100%;
  padding: 0 20px;
}

.score-detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 15px;
  background: var(--background-light);
  border-radius: 10px;
  min-width: 140px;
}

.score-detail-item i {
  font-size: 20px;
  color: var(--primary-color);
}

.detail-content {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.detail-value {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.detail-value.positive {
  color: var(--success-color);
}
/* 健康评分卡片样式 */
.health-score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.score-circle {
  position: relative;
  width: 180px;
  height: 180px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4285f4, #34a853);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(66, 133, 244, 0.2);
}

.score-number {
  font-size: 48px;
  font-weight: bold;
  line-height: 1;
}

.score-text {
  font-size: 18px;
  margin-top: 5px;
}

.score-info {
  position: absolute;
  bottom: 10px;
  right: 10px;
  cursor: help;
  background: rgba(128, 128, 128, 0.6);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.score-info:hover {
  background: rgba(128, 128, 128, 0.8);
  transform: scale(1.1);
}

.score-info i {
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.score-tooltip {
  display: none;
  position: absolute;
  bottom: 100%;
  right: -60px;
  background: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  width: 160px;
  color: var(--text-primary);
  z-index: 100;
  margin-bottom: 10px;
  font-size: 14px;
  animation: fadeIn 0.2s ease;
}

.score-tooltip:after {
  content: '';
  position: absolute;
  bottom: -6px;
  right: 70px;
  width: 12px;
  height: 12px;
  background: white;
  transform: rotate(45deg);
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.score-info:hover .score-tooltip {
  display: block;
}

.tooltip-item {
  padding: 6px 0;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

.tooltip-item:last-child {
  border-bottom: none;
}

.score-details {
  display: flex;
  justify-content: space-around;
  width: 100%;
  padding: 0 20px;
}

.score-detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 15px;
  background: var(--background-light);
  border-radius: 10px;
  min-width: 140px;
}

.score-detail-item i {
  font-size: 20px;
  color: var(--primary-color);
}

.detail-content {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.detail-value {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.detail-value.positive {
  color: var(--success-color);
}
/* 健康评分卡片样式 */
.health-score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.score-circle {
  position: relative;
  width: 180px;
  height: 180px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4285f4, #34a853);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(66, 133, 244, 0.2);
}

.score-number {
  font-size: 48px;
  font-weight: bold;
  line-height: 1;
}

.score-text {
  font-size: 18px;
  margin-top: 5px;
}

.score-info {
  position: absolute;
  bottom: 10px;
  right: 10px;
  cursor: help;
  background: rgba(128, 128, 128, 0.6);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.score-info:hover {
  background: rgba(128, 128, 128, 0.8);
  transform: scale(1.1);
}

.score-info i {
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.score-tooltip {
  display: none;
  position: absolute;
  bottom: 100%;
  right: -60px;
  background: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  width: 160px;
  color: var(--text-primary);
  z-index: 100;
  margin-bottom: 10px;
  font-size: 14px;
  animation: fadeIn 0.2s ease;
}

.score-tooltip:after {
  content: '';
  position: absolute;
  bottom: -6px;
  right: 70px;
  width: 12px;
  height: 12px;
  background: white;
  transform: rotate(45deg);
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.score-info:hover .score-tooltip {
  display: block;
}

.tooltip-item {
  padding: 6px 0;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

.tooltip-item:last-child {
  border-bottom: none;
}

.score-details {
  display: flex;
  justify-content: space-around;
  width: 100%;
  padding: 0 20px;
}

.score-detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 15px;
  background: var(--background-light);
  border-radius: 10px;
  min-width: 140px;
}

.score-detail-item i {
  font-size: 20px;
  color: var(--primary-color);
}

.detail-content {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.detail-value {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.detail-value.positive {
  color: var(--success-color);
}
/* 健康评分卡片样式 */
.health-score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.score-circle {
  position: relative;
  width: 180px;
  height: 180px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4285f4, #34a853);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(66, 133, 244, 0.2);
}

.score-number {
  font-size: 48px;
  font-weight: bold;
  line-height: 1;
}

.score-text {
  font-size: 18px;
  margin-top: 5px;
}

.score-info {
  position: absolute;
  bottom: 10px;
  right: 10px;
  cursor: help;
  background: rgba(128, 128, 128, 0.6);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.score-info:hover {
  background: rgba(128, 128, 128, 0.8);
  transform: scale(1.1);
}

.score-info i {
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.score-tooltip {
  display: none;
  position: absolute;
  bottom: 100%;
  right: -60px;
  background: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  width: 160px;
  color: var(--text-primary);
  z-index: 100;
  margin-bottom: 10px;
  font-size: 14px;
  animation: fadeIn 0.2s ease;
}

.score-tooltip:after {
  content: '';
  position: absolute;
  bottom: -6px;
  right: 70px;
  width: 12px;
  height: 12px;
  background: white;
  transform: rotate(45deg);
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.score-info:hover .score-tooltip {
  display: block;
}

.tooltip-item {
  padding: 6px 0;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

.tooltip-item:last-child {
  border-bottom: none;
}

.score-details {
  display: flex;
  justify-content: space-around;
  width: 100%;
  padding: 0 20px;
}

.score-detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 15px;
  background: var(--background-light);
  border-radius: 10px;
  min-width: 140px;
}

.score-detail-item i {
  font-size: 20px;
  color: var(--primary-color);
}

.detail-content {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.detail-value {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.detail-value.positive {
  color: var(--success-color);
}
/* 健康评分卡片样式 */
.health-score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.score-circle {
  position: relative;
  width: 180px;
  height: 180px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4285f4, #34a853);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(66, 133, 244, 0.2);
}

.score-number {
  font-size: 48px;
  font-weight: bold;
  line-height: 1;
}

.score-text {
  font-size: 18px;
  margin-top: 5px;
}

.score-info {
  position: absolute;
  bottom: 10px;
  right: 10px;
  cursor: help;
  background: rgba(128, 128, 128, 0.6);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.score-info:hover {
  background: rgba(128, 128, 128, 0.8);
  transform: scale(1.1);
}

.score-info i {
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.score-tooltip {
  display: none;
  position: absolute;
  bottom: 100%;
  right: -60px;
  background: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  width: 160px;
  color: var(--text-primary);
  z-index: 100;
  margin-bottom: 10px;
  font-size: 14px;
  animation: fadeIn 0.2s ease;
}

.score-tooltip:after {
  content: '';
  position: absolute;
  bottom: -6px;
  right: 70px;
  width: 12px;
  height: 12px;
  background: white;
  transform: rotate(45deg);
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.score-info:hover .score-tooltip {
  display: block;
}

.tooltip-item {
  padding: 6px 0;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

.tooltip-item:last-child {
  border-bottom: none;
}

.score-details {
  display: flex;
  justify-content: space-around;
  width: 100%;
  padding: 0 20px;
}

.score-detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 15px;
  background: var(--background-light);
  border-radius: 10px;
  min-width: 140px;
}

.score-detail-item i {
  font-size: 20px;
  color: var(--primary-color);
}

.detail-content {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.detail-value {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.detail-value.positive {
  color: var(--success-color);
}
/* 健康评分卡片样式 */
.health-score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.score-circle {
  position: relative;
  width: 180px;
  height: 180px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4285f4, #34a853);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(66, 133, 244, 0.2);
}

.score-number {
  font-size: 48px;
  font-weight: bold;
  line-height: 1;
}

.score-text {
  font-size: 18px;
  margin-top: 5px;
}

.score-info {
  position: absolute;
  bottom: 10px;
  right: 10px;
  cursor: help;
  background: rgba(128, 128, 128, 0.6);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.score-info:hover {
  background: rgba(128, 128, 128, 0.8);
  transform: scale(1.1);
}

.score-info i {
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.score-tooltip {
  display: none;
  position: absolute;
  bottom: 100%;
  right: -60px;
  background: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  width: 160px;
  color: var(--text-primary);
  z-index: 100;
  margin-bottom: 10px;
  font-size: 14px;
  animation: fadeIn 0.2s ease;
}

.score-tooltip:after {
  content: '';
  position: absolute;
  bottom: -6px;
  right: 70px;
  width: 12px;
  height: 12px;
  background: white;
  transform: rotate(45deg);
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.score-info:hover .score-tooltip {
  display: block;
}

.tooltip-item {
  padding: 6px 0;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

.tooltip-item:last-child {
  border-bottom: none;
}

.score-details {
  display: flex;
  justify-content: space-around;
  width: 100%;
  padding: 0 20px;
}

.score-detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 15px;
  background: var(--background-light);
  border-radius: 10px;
  min-width: 140px;
}

.score-detail-item i {
  font-size: 20px;
  color: var(--primary-color);
}

.detail-content {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.detail-value {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.detail-value.positive {
  color: var(--success-color);
}
/* 健康评分卡片样式 */
.health-score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.score-circle {
  position: relative;
  width: 180px;
  height: 180px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4285f4, #34a853);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(66, 133, 244, 0.2);
}

.score-number {
  font-size: 48px;
  font-weight: bold;
  line-height: 1;
}

.score-text {
  font-size: 18px;
  margin-top: 5px;
}

.score-info {
  position: absolute;
  bottom: 10px;
  right: 10px;
  cursor: help;
  background: rgba(128, 128, 128, 0.6);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.score-info:hover {
  background: rgba(128, 128, 128, 0.8);
  transform: scale(1.1);
}

.score-info i {
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.score-tooltip {
  display: none;
  position: absolute;
  bottom: 100%;
  right: -60px;
  background: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  width: 160px;
  color: var(--text-primary);
  z-index: 100;
  margin-bottom: 10px;
  font-size: 14px;
  animation: fadeIn 0.2s ease;
}

.score-tooltip:after {
  content: '';
  position: absolute;
  bottom: -6px;
  right: 70px;
  width: 12px;
  height: 12px;
  background: white;
  transform: rotate(45deg);
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.score-info:hover .score-tooltip {
  display: block;
}

.tooltip-item {
  padding: 6px 0;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

.tooltip-item:last-child {
  border-bottom: none;
}

.score-details {
  display: flex;
  justify-content: space-around;
  width: 100%;
  padding: 0 20px;
}

.score-detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 15px;
  background: var(--background-light);
  border-radius: 10px;
  min-width: 140px;
}

.score-detail-item i {
  font-size: 20px;
  color: var(--primary-color);
}

.detail-content {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.detail-value {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.detail-value.positive {
  color: var(--success-color);
}
/* 健康评分卡片样式 */
.health-score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.score-circle {
  position: relative;
  width: 180px;
  height: 180px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4285f4, #34a853);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(66, 133, 244, 0.2);
}

.score-number {
  font-size: 48px;
  font-weight: bold;
  line-height: 1;
}

.score-text {
  font-size: 18px;
  margin-top: 5px;
}

.score-info {
  position: absolute;
  bottom: 10px;
  right: 10px;
  cursor: help;
  background: rgba(128, 128, 128, 0.6);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.score-info:hover {
  background: rgba(128, 128, 128, 0.8);
  transform: scale(1.1);
}

.score-info i {
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.score-tooltip {
  display: none;
  position: absolute;
  bottom: 100%;
  right: -60px;
  background: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  width: 160px;
  color: var(--text-primary);
  z-index: 100;
  margin-bottom: 10px;
  font-size: 14px;
  animation: fadeIn 0.2s ease;
}

.score-tooltip:after {
  content: '';
  position: absolute;
  bottom: -6px;
  right: 70px;
  width: 12px;
  height: 12px;
  background: white;
  transform: rotate(45deg);
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.score-info:hover .score-tooltip {
  display: block;
}

.tooltip-item {
  padding: 6px 0;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

.tooltip-item:last-child {
  border-bottom: none;
}

.score-details {
  display: flex;
  justify-content: space-around;
  width: 100%;
  padding: 0 20px;
}

.score-detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 15px;
  background: var(--background-light);
  border-radius: 10px;
  min-width: 140px;
}

.score-detail-item i {
  font-size: 20px;
  color: var(--primary-color);
}

.detail-content {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.detail-value {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.detail-value.positive {
  color: var(--success-color);
}
/* 健康评分卡片样式 */
.health-score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.score-circle {
  position: relative;
  width: 180px;
  height: 180px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4285f4, #34a853);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(66, 133, 244, 0.2);
}

.score-number {
  font-size: 48px;
  font-weight: bold;
  line-height: 1;
}

.score-text {
  font-size: 18px;
  margin-top: 5px;
}

.score-info {
  position: absolute;
  bottom: 10px;
  right: 10px;
  cursor: help;
  background: rgba(128, 128, 128, 0.6);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.score-info:hover {
  background: rgba(128, 128, 128, 0.8);
  transform: scale(1.1);
}

.score-info i {
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.score-tooltip {
  display: none;
  position: absolute;
  bottom: 100%;
  right: -60px;
  background: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  width: 160px;
  color: var(--text-primary);
  z-index: 100;
  margin-bottom: 10px;
  font-size: 14px;
  animation: fadeIn 0.2s ease;
}

.score-tooltip:after {
  content: '';
  position: absolute;
  bottom: -6px;
  right: 70px;
  width: 12px;
  height: 12px;
  background: white;
  transform: rotate(45deg);
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.score-info:hover .score-tooltip {
  display: block;
}

.tooltip-item {
  padding: 6px 0;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

.tooltip-item:last-child {
  border-bottom: none;
}

.score-details {
  display: flex;
  justify-content: space-around;
  width: 100%;
  padding: 0 20px;
}

.score-detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 15px;
  background: var(--background-light);
  border-radius: 10px;
  min-width: 140px;
}

.score-detail-item i {
  font-size: 20px;
  color: var(--primary-color);
}

.detail-content {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.detail-value {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.detail-value.positive {
  color: var(--success-color);
}
/* 健康评分卡片样式 */
.health-score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.score-circle {
  position: relative;
  width: 180px;
  height: 180px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4285f4, #34a853);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(66, 133, 244, 0.2);
}

.score-number {
  font-size: 48px;
  font-weight: bold;
  line-height: 1;
}

.score-text {
  font-size: 18px;
  margin-top: 5px;
}

.score-info {
  position: absolute;
  bottom: 10px;
  right: 10px;
  cursor: help;
  background: rgba(128, 128, 128, 0.6);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.score-info:hover {
  background: rgba(128, 128, 128, 0.8);
  transform: scale(1.1);
}

.score-info i {
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.score-tooltip {
  display: none;
  position: absolute;
  bottom: 100%;
  right: -60px;
  background: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  width: 160px;
  color: var(--text-primary);
  z-index: 100;
  margin-bottom: 10px;
  font-size: 14px;
  animation: fadeIn 0.2s ease;
}

.score-tooltip:after {
  content: '';
  position: absolute;
  bottom: -6px;
  right: 70px;
  width: 12px;
  height: 12px;
  background: white;
  transform: rotate(45deg);
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.score-info:hover .score-tooltip {
  display: block;
}

.tooltip-item {
  padding: 6px 0;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

.tooltip-item:last-child {
  border-bottom: none;
}

.score-details {
  display: flex;
  justify-content: space-around;
  width: 100%;
  padding: 0 20px;
}

.score-detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 15px;
  background: var(--background-light);
  border-radius: 10px;
  min-width: 140px;
}

.score-detail-item i {
  font-size: 20px;
  color: var(--primary-color);
}

.detail-content {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.detail-value {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.detail-value.positive {
  color: var(--success-color);
}
/* 健康评分卡片样式 */
.health-score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.score-circle {
  position: relative;
  width: 180px;
  height: 180px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4285f4, #34a853);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(66, 133, 244, 0.2);
}

.score-number {
  font-size: 48px;
  font-weight: bold;
  line-height: 1;
}

.score-text {
  font-size: 18px;
  margin-top: 5px;
}

.score-info {
  position: absolute;
  bottom: 10px;
  right: 10px;
  cursor: help;
  background: rgba(128, 128, 128, 0.6);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.score-info:hover {
  background: rgba(128, 128, 128, 0.8);
  transform: scale(1.1);
}

.score-info i {
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.score-tooltip {
  display: none;
  position: absolute;
  bottom: 100%;
  right: -60px;
  background: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  width: 160px;
  color: var(--text-primary);
  z-index: 100;
  margin-bottom: 10px;
  font-size: 14px;
  animation: fadeIn 0.2s ease;
}

.score-tooltip:after {
  content: '';
  position: absolute;
  bottom: -6px;
  right: 70px;
  width: 12px;
  height: 12px;
  background: white;
  transform: rotate(45deg);
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.score-info:hover .score-tooltip {
  display: block;
}

.tooltip-item {
  padding: 6px 0;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

.tooltip-item:last-child {
  border-bottom: none;
}

.score-details {
  display: flex;
  justify-content: space-around;
  width: 100%;
  padding: 0 20px;
}

.score-detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 15px;
  background: var(--background-light);
  border-radius: 10px;
  min-width: 140px;
}

.score-detail-item i {
  font-size: 20px;
  color: var(--primary-color);
}

.detail-content {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.detail-value {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.detail-value.positive {
  color: var(--success-color);
}
/* 健康评分卡片样式 */
.health-score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.score-circle {
  position: relative;
  width: 180px;
  height: 180px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4285f4, #34a853);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(66, 133, 244, 0.2);
}

.score-number {
  font-size: 48px;
  font-weight: bold;
  line-height: 1;
}

.score-text {
  font-size: 18px;
  margin-top: 5px;
}

.score-info {
  position: absolute;
  bottom: 10px;
  right: 10px;
  cursor: help;
  background: rgba(128, 128, 128, 0.6);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.score-info:hover {
  background: rgba(128, 128, 128, 0.8);
  transform: scale(1.1);
}

.score-info i {
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.score-tooltip {
  display: none;
  position: absolute;
  bottom: 100%;
  right: -60px;
  background: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  width: 160px;
  color: var(--text-primary);
  z-index: 100;
  margin-bottom: 10px;
  font-size: 14px;
  animation: fadeIn 0.2s ease;
}

.score-tooltip:after {
  content: '';
  position: absolute;
  bottom: -6px;
  right: 70px;
  width: 12px;
  height: 12px;
  background: white;
  transform: rotate(45deg);
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.score-info:hover .score-tooltip {
  display: block;
}

.tooltip-item {
  padding: 6px 0;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

.tooltip-item:last-child {
  border-bottom: none;
}

.score-details {
  display: flex;
  justify-content: space-around;
  width: 100%;
  padding: 0 20px;
}

.score-detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 15px;
  background: var(--background-light);
  border-radius: 10px;
  min-width: 140px;
}

.score-detail-item i {
  font-size: 20px;
  color: var(--primary-color);
}

.detail-content {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.detail-value {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.detail-value.positive {
  color: var(--success-color);
}
/* 健康评分卡片样式 */
.health-score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.score-circle {
  position: relative;
  width: 180px;
  height: 180px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4285f4, #34a853);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(66, 133, 244, 0.2);
}

.score-number {
  font-size: 48px;
  font-weight: bold;
  line-height: 1;
}

.score-text {
  font-size: 18px;
  margin-top: 5px;
}

.score-info {
  position: absolute;
  bottom: 10px;
  right: 10px;
  cursor: help;
  background: rgba(128, 128, 128, 0.6);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.score-info:hover {
  background: rgba(128, 128, 128, 0.8);
  transform: scale(1.1);
}

.score-info i {
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.score-tooltip {
  display: none;
  position: absolute;
  bottom: 100%;
  right: -60px;
  background: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  width: 160px;
  color: var(--text-primary);
  z-index: 100;
  margin-bottom: 10px;
  font-size: 14px;
  animation: fadeIn 0.2s ease;
}

.score-tooltip:after {
  content: '';
  position: absolute;
  bottom: -6px;
  right: 70px;
  width: 12px;
  height: 12px;
  background: white;
  transform: rotate(45deg);
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.score-info:hover .score-tooltip {
  display: block;
}

.tooltip-item {
  padding: 6px 0;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

.tooltip-item:last-child {
  border-bottom: none;
}

.score-details {
  display: flex;
  justify-content: space-around;
  width: 100%;
  padding: 0 20px;
}

.score-detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 15px;
  background: var(--background-light);
  border-radius: 10px;
  min-width: 140px;
}

.score-detail-item i {
  font-size: 20px;
  color: var(--primary-color);
}

.detail-content {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.detail-value {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.detail-value.positive {
  color: var(--success-color);
}
/* 健康评分卡片样式 */
.health-score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.score-circle {
  position: relative;
  width: 180px;
  height: 180px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4285f4, #34a853);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(66, 133, 244, 0.2);
}

.score-number {
  font-size: 48px;
  font-weight: bold;
  line-height: 1;
}

.score-text {
  font-size: 18px;
  margin-top: 5px;
}

.score-info {
  position: absolute;
  bottom: 10px;
  right: 10px;
  cursor: help;
  background: rgba(128, 128, 128, 0.6);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.score-info:hover {
  background: rgba(128, 128, 128, 0.8);
  transform: scale(1.1);
}

.score-info i {
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.score-tooltip {
  display: none;
  position: absolute;
  bottom: 100%;
  right: -60px;
  background: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  width: 160px;
  color: var(--text-primary);
  z-index: 100;
  margin-bottom: 10px;
  font-size: 14px;
  animation: fadeIn 0.2s ease;
}

.score-tooltip:after {
  content: '';
  position: absolute;
  bottom: -6px;
  right: 70px;
  width: 12px;
  height: 12px;
  background: white;
  transform: rotate(45deg);
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.score-info:hover .score-tooltip {
  display: block;
}

.tooltip-item {
  padding: 6px 0;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

.tooltip-item:last-child {
  border-bottom: none;
}

.score-details {
  display: flex;
  justify-content: space-around;
  width: 100%;
  padding: 0 20px;
}

.score-detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 15px;
  background: var(--background-light);
  border-radius: 10px;
  min-width: 140px;
}

.score-detail-item i {
  font-size: 20px;
  color: var(--primary-color);
}

.detail-content {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.detail-value {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.detail-value.positive {
  color: var(--success-color);
}
/* 健康评分卡片样式 */
.health-score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.score-circle {
  position: relative;
  width: 180px;
  height: 180px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4285f4, #34a853);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(66, 133, 244, 0.2);
}

.score-number {
  font-size: 48px;
  font-weight: bold;
  line-height: 1;
}

.score-text {
  font-size: 18px;
  margin-top: 5px;
}

.score-info {
  position: absolute;
  bottom: 10px;
  right: 10px;
  cursor: help;
  background: rgba(128, 128, 128, 0.6);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.score-info:hover {
  background: rgba(128, 128, 128, 0.8);
  transform: scale(1.1);
}

.score-info i {
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.score-tooltip {
  display: none;
  position: absolute;
  bottom: 100%;
  right: -60px;
  background: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  width: 160px;
  color: var(--text-primary);
  z-index: 100;
  margin-bottom: 10px;
  font-size: 14px;
  animation: fadeIn 0.2s ease;
}

.score-tooltip:after {
  content: '';
  position: absolute;
  bottom: -6px;
  right: 70px;
  width: 12px;
  height: 12px;
  background: white;
  transform: rotate(45deg);
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.score-info:hover .score-tooltip {
  display: block;
}

.tooltip-item {
  padding: 6px 0;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

.tooltip-item:last-child {
  border-bottom: none;
}

.score-details {
  display: flex;
  justify-content: space-around;
  width: 100%;
  padding: 0 20px;
}

.score-detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 15px;
  background: var(--background-light);
  border-radius: 10px;
  min-width: 140px;
}

.score-detail-item i {
  font-size: 20px;
  color: var(--primary-color);
}

.detail-content {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.detail-value {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.detail-value.positive {
  color: var(--success-color);
}
/* 健康评分卡片样式 */
.health-score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.score-circle {
  position: relative;
  width: 180px;
  height: 180px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4285f4, #34a853);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(66, 133, 244, 0.2);
}

.score-number {
  font-size: 48px;
  font-weight: bold;
  line-height: 1;
}

.score-text {
  font-size: 18px;
  margin-top: 5px;
}

.score-info {
  position: absolute;
  bottom: 10px;
  right: 10px;
  cursor: help;
  background: rgba(128, 128, 128, 0.6);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.score-info:hover {
  background: rgba(128, 128, 128, 0.8);
  transform: scale(1.1);
}

.score-info i {
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.score-tooltip {
  display: none;
  position: absolute;
  bottom: 100%;
  right: -60px;
  background: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  width: 160px;
  color: var(--text-primary);
  z-index: 100;
  margin-bottom: 10px;
  font-size: 14px;
  animation: fadeIn 0.2s ease;
}

.score-tooltip:after {
  content: '';
  position: absolute;
  bottom: -6px;
  right: 70px;
  width: 12px;
  height: 12px;
  background: white;
  transform: rotate(45deg);
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.score-info:hover .score-tooltip {
  display: block;
}

.tooltip-item {
  padding: 6px 0;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
}

.tooltip-item:last-child {
  border-bottom: none;
}

.score-details {
  display: flex;
  justify-content: space-around;
  width: 100%;
  padding: 0 20px;
}

.score-detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 15px;
  background: var(--background-light);
  border-radius: 10px;
  min-width: 140px;
}

.score-detail-item i {
  font-size: 20px;
  color: var(--primary-color);
}

.detail-content {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.detail-value {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.detail-value.positive {
  color: var(--success-color);
}