/**
 * Service Worker - 缓存策略和离线支持
 * 实现多种缓存策略以优化应用性能
 */

const CACHE_NAME = 'health-app-v1.0.0';
const API_CACHE_NAME = 'health-app-api-v1.0.0';

// 需要缓存的静态资源
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/login.html',
  '/reports.html',
  '/report-detail.html',
  '/trends.html',
  '/compare.html',
  '/callback.html',
  '/css/styles.css',
  '/css/mobile.css',
  '/css/charts.css',
  '/js/utils/api.js',
  '/js/utils/storage.js',
  '/js/utils/performance.js',
  '/js/auth/auth.js',
  '/js/auth/callback.js',
  '/js/components/charts.js',
  '/js/pages/home.js',
  '/js/pages/reports.js',
  '/js/pages/detail.js',
  '/js/pages/trends.js',
  '/js/pages/compare.js',
  'https://cdn.jsdelivr.net/npm/chart.js',
  'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css'
];

// API路径模式
const API_PATTERNS = [
  /^\/api\/auth\/verify$/,
  /^\/api\/users\/me$/,
  /^\/api\/reports/,
  /^\/api\/stats/
];

/**
 * Service Worker安装事件
 */
self.addEventListener('install', event => {
  console.log('Service Worker installing...');
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Caching static assets...');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('Static assets cached successfully');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('Failed to cache static assets:', error);
      })
  );
});

/**
 * Service Worker激活事件
 */
self.addEventListener('activate', event => {
  console.log('Service Worker activating...');
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            // 删除旧版本缓存
            if (cacheName !== CACHE_NAME && cacheName !== API_CACHE_NAME) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker activated');
        return self.clients.claim();
      })
  );
});

/**
 * 网络请求拦截
 */
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);
  
  // 只处理同源请求和CDN资源
  if (url.origin !== location.origin && !isCDNResource(url)) {
    return;
  }
  
  // 根据请求类型选择缓存策略
  if (isAPIRequest(request)) {
    event.respondWith(handleAPIRequest(request));
  } else if (isStaticAsset(request)) {
    event.respondWith(handleStaticAsset(request));
  } else {
    event.respondWith(handleNavigationRequest(request));
  }
});

/**
 * 判断是否为API请求
 * @param {Request} request - 请求对象
 * @returns {boolean} 是否为API请求
 */
function isAPIRequest(request) {
  const url = new URL(request.url);
  return API_PATTERNS.some(pattern => pattern.test(url.pathname));
}

/**
 * 判断是否为静态资源
 * @param {Request} request - 请求对象
 * @returns {boolean} 是否为静态资源
 */
function isStaticAsset(request) {
  const url = new URL(request.url);
  return /\.(css|js|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot)$/.test(url.pathname) ||
         isCDNResource(url);
}

/**
 * 判断是否为CDN资源
 * @param {URL} url - URL对象
 * @returns {boolean} 是否为CDN资源
 */
function isCDNResource(url) {
  const cdnDomains = [
    'cdn.jsdelivr.net',
    'cdnjs.cloudflare.com',
    'fonts.googleapis.com',
    'fonts.gstatic.com'
  ];
  return cdnDomains.includes(url.hostname);
}

/**
 * 处理API请求 - Network First策略
 * @param {Request} request - 请求对象
 * @returns {Promise<Response>} 响应Promise
 */
async function handleAPIRequest(request) {
  try {
    // 尝试网络请求
    const networkResponse = await fetch(request);
    
    // 如果是GET请求且响应成功，缓存响应
    if (request.method === 'GET' && networkResponse.ok) {
      const cache = await caches.open(API_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Network request failed, trying cache:', error);
    
    // 网络失败，尝试从缓存获取
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // 缓存也没有，返回离线页面或错误响应
    return new Response(
      JSON.stringify({ 
        error: 'Network unavailable', 
        message: '网络不可用，请检查网络连接' 
      }),
      {
        status: 503,
        statusText: 'Service Unavailable',
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

/**
 * 处理静态资源 - Cache First策略
 * @param {Request} request - 请求对象
 * @returns {Promise<Response>} 响应Promise
 */
async function handleStaticAsset(request) {
  // 先尝试从缓存获取
  const cachedResponse = await caches.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    // 缓存中没有，从网络获取
    const networkResponse = await fetch(request);
    
    // 缓存响应
    if (networkResponse.ok) {
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.error('Failed to fetch static asset:', error);
    
    // 返回离线占位符
    return new Response('Resource not available offline', {
      status: 503,
      statusText: 'Service Unavailable'
    });
  }
}

/**
 * 处理导航请求 - Network First with Cache Fallback
 * @param {Request} request - 请求对象
 * @returns {Promise<Response>} 响应Promise
 */
async function handleNavigationRequest(request) {
  try {
    // 尝试网络请求
    const networkResponse = await fetch(request);
    
    // 缓存成功的响应
    if (networkResponse.ok) {
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Navigation request failed, trying cache:', error);
    
    // 网络失败，尝试从缓存获取
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // 缓存也没有，返回离线页面
    const offlineResponse = await caches.match('/index.html');
    return offlineResponse || new Response('Page not available offline', {
      status: 503,
      statusText: 'Service Unavailable'
    });
  }
}

/**
 * 后台同步事件
 */
self.addEventListener('sync', event => {
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

/**
 * 执行后台同步
 */
async function doBackgroundSync() {
  try {
    // 这里可以实现数据同步逻辑
    console.log('Background sync completed');
  } catch (error) {
    console.error('Background sync failed:', error);
  }
}

/**
 * 推送通知事件
 */
self.addEventListener('push', event => {
  const options = {
    body: event.data ? event.data.text() : '您有新的体检报告',
    icon: '/images/icon-192x192.png',
    badge: '/images/badge-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: '查看详情',
        icon: '/images/checkmark.png'
      },
      {
        action: 'close',
        title: '关闭',
        icon: '/images/xmark.png'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification('体检应用', options)
  );
});

/**
 * 通知点击事件
 */
self.addEventListener('notificationclick', event => {
  event.notification.close();
  
  if (event.action === 'explore') {
    // 打开应用
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

/**
 * 消息事件 - 与主线程通信
 */
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({ version: CACHE_NAME });
  }
});
