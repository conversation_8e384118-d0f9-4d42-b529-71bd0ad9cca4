# 移动端体检应用项目文档

## 1. 项目概述

### 1.1 项目简介

移动端体检应用是一个基于HTML/CSS/JavaScript和Node.js开发的Web应用，旨在帮助用户管理和分析体检数据。该应用提供了用户认证、体检报告查询、健康趋势分析和数据对比等功能，使用户能够更好地了解和管理自己的健康状况。

### 1.2 功能特点

- **OAuth2用户登录**：支持GitHub账号登录
- **数据查询**：按时间轴查看历史体检报告、报告详情页、异常指标高亮标记
- **统计分析**：指标趋势分析、体检数据对比、健康评分系统
- **响应式设计**：适配移动端和桌面端
- **深色模式支持**：根据系统设置自动切换深色/浅色模式

### 1.3 技术栈

- **前端**：HTML5, CSS3, JavaScript (ES6+)
- **后端**：Node.js, Express
- **数据库**：SQL (设计)
- **认证**：OAuth2 (GitHub)
- **图表**：Chart.js
- **图标**：Font Awesome

## 2. 系统架构

### 2.1 整体架构

该应用采用前后端分离的架构，前端负责用户界面和交互，后端负责数据处理和API提供。


### 2.2 数据流

1. 用户通过浏览器访问应用
2. 前端向后端发送API请求
3. 后端处理请求，与数据库交互
4. 后端返回数据给前端
5. 前端渲染数据，展示给用户



## 3. 数据库设计

### 3.1 ER图

+---------------+       +-------------------+       +----------------+
|     用户      |       |     体检报告      |       |   字段映射     |
|    (users)    |       | (health_reports)  |       |(field_mappings)|
+---------------+       +-------------------+       +----------------+
| PK: id        |<----->| PK: id            |       | PK: id         |
| name          |  1:N  | FK: user_id       |       | field_name     |
| email         |       | report_date       |       | display_name   |
| avatar_url    |       | hospital          |       | category       |
| gender        |       | doctor            |       | unit           |
| birth_date    |       | health_score      |       | min_value      |
| height        |       | report_summary    |       | max_value      |
| weight        |       | created_at        |       | description    |
| created_at    |       | updated_at        |       | is_critical    |
| updated_at    |       |                   |       | created_at     |
+---------------+       +--------^----------+       | updated_at     |
                                 | 1:1                +----------------+
                                 |
                 +---------------+---------------+
                 |               |               |
        +--------v-----+  +------v-------+  +----v---------+
        | 血液检查     |  | 肝功能检查   |  | 肾功能检查   |
        |(blood_tests) |  |(liver_tests) |  |(kidney_tests)|
        +--------------+  +--------------+  +--------------+
        | PK: id       |  | PK: id       |  | PK: id       |
        | FK: report_id|  | FK: report_id|  | FK: report_id|
        | wbc          |  | alt          |  | bun          |
        | rbc          |  | ast          |  | cre          |
        | hgb          |  | alp          |  | ua           |
        | plt          |  | ggt          |  | created_at   |
        | cholesterol  |  | tbil         |  | updated_at   |
        | triglycerides|  | dbil         |  +--------------+
        | hdl          |  | tp           |
        | ldl          |  | alb          |        +--------------+
        | glucose      |  | created_at   |        | 心电图检查   |
        | created_at   |  | updated_at   |        | (ecg_tests)  |
        | updated_at   |  +--------------+        +--------------+
        +--------------+                          | PK: id       |
                                                  | FK: report_id|
                                                  | heart_rate   |
                                                  | pr_interval  |
                                                  | qrs_duration |
                                                  | qt_interval  |
                                                  | result       |
                                                  | created_at   |
                                                  | updated_at   |
                                                  +--------------+

### 3.2 表结构

#### 用户表 (users)

| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | VARCHAR(50) | 主键，用户ID |
| name | VARCHAR(100) | 用户姓名 |
| email | VARCHAR(100) | 用户邮箱 |
| avatar_url | VARCHAR(255) | 头像URL |
| gender | VARCHAR(10) | 性别 |
| birth_date | DATE | 出生日期 |
| height | FLOAT | 身高 |
| weight | FLOAT | 体重 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

#### 体检报告表 (health_reports)

| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | VARCHAR(36) | 主键，报告ID |
| user_id | VARCHAR(50) | 外键，用户ID |
| report_date | DATE | 体检日期 |
| hospital | VARCHAR(100) | 医院名称 |
| doctor | VARCHAR(100) | 医生姓名 |
| health_score | INT | 健康评分 |
| report_summary | TEXT | 报告摘要 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

#### 血液检查表 (blood_tests)

| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | VARCHAR(36) | 主键，检查ID |
| report_id | VARCHAR(36) | 外键，报告ID |
| wbc | FLOAT | 白细胞 |
| rbc | FLOAT | 红细胞 |
| hgb | FLOAT | 血红蛋白 |
| plt | FLOAT | 血小板 |
| cholesterol | FLOAT | 胆固醇 |
| triglycerides | FLOAT | 甘油三酯 |
| hdl | FLOAT | 高密度脂蛋白 |
| ldl | FLOAT | 低密度脂蛋白 |
| glucose | FLOAT | 血糖 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

#### 肝功能检查表 (liver_function_tests)

| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | VARCHAR(36) | 主键，检查ID |
| report_id | VARCHAR(36) | 外键，报告ID |
| alt | FLOAT | 丙氨酸转氨酶 |
| ast | FLOAT | 天门冬氨酸转氨酶 |
| alp | FLOAT | 碱性磷酸酶 |
| ggt | FLOAT | γ-谷氨酰转肽酶 |
| tbil | FLOAT | 总胆红素 |
| dbil | FLOAT | 直接胆红素 |
| tp | FLOAT | 总蛋白 |
| alb | FLOAT | 白蛋白 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

#### 肾功能检查表 (kidney_function_tests)

| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | VARCHAR(36) | 主键，检查ID |
| report_id | VARCHAR(36) | 外键，报告ID |
| bun | FLOAT | 尿素氮 |
| cre | FLOAT | 肌酐 |
| ua | FLOAT | 尿酸 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

#### 心电图检查表 (ecg_tests)

| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | VARCHAR(36) | 主键，检查ID |
| report_id | VARCHAR(36) | 外键，报告ID |
| heart_rate | INT | 心率 |
| pr_interval | FLOAT | PR间期 |
| qrs_duration | FLOAT | QRS时限 |
| qt_interval | FLOAT | QT间期 |
| result | TEXT | 检查结果 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

#### 字段映射表 (field_mappings)

| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | VARCHAR(36) | 主键，映射ID |
| field_name | VARCHAR(50) | 字段名称 |
| display_name | VARCHAR(100) | 显示名称 |
| category | VARCHAR(50) | 类别 |
| unit | VARCHAR(20) | 单位 |
| min_value | FLOAT | 最小值 |
| max_value | FLOAT | 最大值 |
| description | TEXT | 描述 |
| is_critical | BOOLEAN | 是否关键指标 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

## 4. 项目结构

项目采用模块化的文件结构，清晰地分离了前端和后端代码，便于维护和扩展。

```
/health-check-app
│
├── /public                  # 静态资源目录
│   ├── /css                 # 样式文件
│   │   ├── styles.css       # 主样式文件
│   │   ├── charts.css       # 图表样式
│   │   └── mobile.css       # 移动端响应式样式
│   │
│   ├── /js                  # JavaScript文件
│   │   ├── /auth            # 认证相关
│   │   │   ├── auth.js      # 认证逻辑
│   │   │   └── callback.js  # OAuth回调处理
│   │   │
│   │   ├── /components      # 组件
│   │   │   ├── header.js    # 头部组件
│   │   │   ├── footer.js    # 底部组件
│   │   │   ├── charts.js    # 图表组件
│   │   │   └── timeline.js  # 时间轴组件
│   │   │
│   │   ├── /pages           # 页面逻辑
│   │   │   ├── home.js      # 首页
│   │   │   ├── reports.js   # 报告列表
│   │   │   ├── detail.js    # 报告详情
│   │   │   ├── trends.js    # 趋势分析
│   │   │   └── compare.js   # 数据对比
│   │   │
│   │   ├── /utils           # 工具函数
│   │   │   ├── api.js       # API请求
│   │   │   ├── storage.js   # 本地存储
│   │   │   └── helpers.js   # 辅助函数
│   │   │
│   │   └── app.js           # 主应用逻辑
│   │
│   ├── /images              # 图片资源
│   │
│   ├── /lib                 # 第三方库
│   │   ├── chart.min.js     # Chart.js图表库
│   │   └── ...
│   │
│   ├── index.html           # 首页
│   ├── login.html           # 登录页
│   ├── callback.html        # OAuth回调页
│   ├── reports.html         # 报告列表页
│   ├── report-detail.html   # 报告详情页
│   ├── trends.html          # 趋势分析页
│   └── compare.html         # 数据对比页
│
├── /server                  # 服务器端代码
│   ├── /controllers         # 控制器
│   │   ├── authController.js    # 认证控制器
│   │   ├── reportController.js  # 报告控制器
│   │   ├── userController.js    # 用户控制器
│   │   └── statsController.js   # 统计控制器
│   │
│   ├── /models              # 数据模型
│   │   ├── User.js          # 用户模型
│   │   ├── Report.js        # 报告模型
│   │   ├── BloodTest.js     # 血液检查模型
│   │   ├── LiverTest.js     # 肝功能检查模型
│   │   ├── KidneyTest.js    # 肾功能检查模型
│   │   ├── EcgTest.js       # 心电图检查模型
│   │   └── FieldMapping.js  # 字段映射模型
│   │
│   ├── /routes              # 路由
│   │   ├── authRoutes.js    # 认证路由
│   │   ├── reportRoutes.js  # 报告路由
│   │   ├── userRoutes.js    # 用户路由
│   │   └── statsRoutes.js   # 统计路由
│   │
│   ├── /utils               # 工具函数
│   │   ├── database.js      # 数据库连接
│   │   ├── auth.js          # 认证工具
│   │   └── helpers.js       # 辅助函数
│   │
│   ├── /middleware          # 中间件
│   │   ├── auth.js          # 认证中间件
│   │   └── errorHandler.js  # 错误处理中间件
│   │
│   └── server.js            # 服务器入口文件
│
├── /database                # 数据库相关
│   ├── schema.sql           # 数据库模式
│   └── seed.js              # 种子数据
│
├── /config                  # 配置文件
│   ├── default.js           # 默认配置
│   └── production.js        # 生产环境配置
│
├── package.json             # 项目依赖
├── .env                     # 环境变量
└── README.md                # 项目说明
```



## 5. 功能模块

### 5.1 用户认证模块

#### 5.1.1 GitHub OAuth2登录

应用使用GitHub OAuth2协议实现用户认证，流程如下：

1. 用户点击"使用GitHub账号登录"按钮
2. 应用重定向到GitHub授权页面
3. 用户授权应用访问其GitHub账号
4. GitHub重定向回应用，带上授权码
5. 应用使用授权码向GitHub请求访问令牌
6. 应用使用访问令牌获取用户信息
7. 应用创建JWT令牌并返回给前端
8. 前端存储JWT令牌，完成登录

![GitHub OAuth2登录流程](这里放置GitHub OAuth2登录流程图)

#### 5.1.2 认证状态管理

应用使用JWT（JSON Web Token）进行认证状态管理，主要功能包括：

- 令牌验证：验证用户令牌的有效性
- 令牌刷新：在令牌即将过期时刷新令牌
- 自动登出：令牌过期时自动登出
- 会话过期提醒：在会话即将过期时提醒用户

#### 5.1.3 安全措施

- CSRF保护：使用状态参数防止跨站请求伪造
- 令牌过期：设置令牌有效期，减少被盗用风险
- 令牌黑名单：支持将已登出的令牌加入黑名单
- HTTPS支持：在生产环境中使用HTTPS加密传输

### 5.2 数据查询模块

#### 5.2.1 体检报告时间轴

用户可以按时间轴查看历史体检报告，主要功能包括：

- 按年份分组显示报告
- 显示报告日期、医院和健康评分
- 显示报告摘要和异常指标标签
- 提供报告详情链接



#### 5.2.2 报告详情页

用户可以查看体检报告的详细信息，主要功能包括：

- 显示报告基本信息（日期、医院、健康评分）
- 按类别（血液、肝功能、肾功能、心电图）分组显示检查结果
- 显示指标值、参考范围和状态（正常、偏高、偏低）
- 高亮显示异常指标
- 提供异常指标分析和建议
- 显示历史趋势图表

#### 5.2.3 异常指标高亮

系统自动识别并高亮显示异常指标，主要功能包括：

- 根据参考范围判断指标状态
- 使用不同颜色标识不同状态（正常、偏高、偏低）
- 在报告列表和详情页中突出显示异常指标
- 提供异常指标的解释和建议



### 5.3 统计分析模块

#### 5.3.1 指标趋势分析

用户可以查看健康指标的历史趋势，主要功能包括：

- 选择指标（血糖、胆固醇、血压等）
- 选择时间范围（3个月、6个月、1年、全部）
- 显示趋势图表（折线图或柱状图）
- 显示参考范围线
- 提供趋势分析和建议



#### 5.3.2 体检数据对比

用户可以将自己的体检数据与同龄人群进行对比，主要功能包括：

- 选择对比类型（人群对比或个人历史对比）
- 选择年龄段、性别和指标
- 显示对比图表
- 显示排名情况
- 提供改进建议



#### 5.3.3 健康评分系统

系统根据用户的体检数据计算健康评分，主要功能包括：

- 百分制健康评分
- 仪表盘显示
- 评分等级（优秀、良好、一般、较差、需注意）
- 评分解释和改进建议



## 6. 前端实现

### 6.1 页面结构

#### 6.1.1 登录页面

登录页面提供GitHub登录选项，以及未来可能支持的手机号和邮箱登录选项。

```html
<!-- 登录按钮 -->
<button id="github-login" class="login-btn">
  <svg viewBox="0 0 24 24" width="24" height="24">
    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
  </svg>
  使用 GitHub 账号登录
</button>
```
![登录页](image-9.png)

#### 6.1.2 首页

首页显示用户的健康概览，包括健康评分、最近体检报告和健康趋势。
![首页1](image.png)
![首页2](image-10.png)
![首页3](image-11.png)

#### 6.1.3 报告列表页

报告列表页以时间轴形式展示用户的历史体检报告。
![报告列表页](image-1.png)


#### 6.1.4 报告详情页

报告详情页展示体检报告的详细信息，按类别分组显示检查结果。

![报告详情页1](image-6.png)
![报告详情页2](image-7.png)
![报告详情页3](image-8.png)

#### 6.1.5 趋势分析页

趋势分析页展示用户选择的健康指标的历史趋势。

![趋势分析页1](image-2.png)
![趋势分析页2](image-5.png)

#### 6.1.6 数据对比页

数据对比页展示用户的体检数据与同龄人群的对比情况。

![对比页1](image-3.png)
![对比页2](image-4.png)

### 6.2 样式设计

#### 6.2.1 主题色

应用使用以下主题色：

- 主色：#4285f4（蓝色）
- 次色：#34a853（绿色）
- 警告色：#fbbc05（黄色）
- 错误色：#ea4335（红色）

#### 6.2.2 响应式设计

应用使用媒体查询实现响应式设计，适配不同屏幕尺寸：

```css
/* 响应式设计 */
@media (max-width: 480px) {
  .container {
    padding: 15px;
  }
  
  .card {
    padding: 15px;
  }
}
```

#### 6.2.3 深色模式

应用支持深色模式，根据系统设置自动切换：

```css
/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: #e8eaed;
    --text-secondary: #9aa0a6;
    --card-background: #202124;
    --border-color: #5f6368;
    --background-light: #202124;
    --background-dark: #303134;
  }
}
```

### 6.3 JavaScript模块

#### 6.3.1 认证模块

认证模块处理用户登录、登出和认证状态管理。

#### 6.3.2 API模块

API模块处理与后端的通信，封装了各种API请求。

#### 6.3.3 存储模块

存储模块处理本地存储，包括令牌、用户信息和缓存数据。

#### 6.3.4 图表模块

图表模块使用Chart.js创建各种图表，包括健康评分仪表盘、趋势图和对比图。

## 7. 后端实现

### 7.1 服务器设置

服务器使用Express框架，主要设置包括：

```javascript
// 中间件
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// 会话配置
app.use(session({
  secret: process.env.JWT_SECRET || 'your_session_secret',
  resave: false,
  saveUninitialized: false,
  cookie: { secure: process.env.NODE_ENV === 'production' }
}));
```

### 7.2 认证路由

认证路由处理GitHub OAuth2登录流程：

```javascript
// 路由 - GitHub OAuth登录
app.get('/auth/github', (req, res) => {
  const state = req.query.state || generateRandomState();
  req.session.oauthState = state;
  
  const githubAuthUrl = `https://github.com/login/oauth/authorize?client_id=${GITHUB_CLIENT_ID}&redirect_uri=${REDIRECT_URI}&scope=user:email read:user&state=${state}`;
  res.redirect(githubAuthUrl);
});

// 路由 - GitHub OAuth回调
app.get('/auth/github/callback', async (req, res) => {
  // 处理回调逻辑
});
```

### 7.3 API路由

API路由提供各种数据操作接口：

```javascript
// API路由 - 验证令牌
app.get('/api/auth/verify', (req, res) => {
  // 验证令牌逻辑
});

// API路由 - 刷新令牌
app.post('/api/auth/refresh', (req, res) => {
  // 刷新令牌逻辑
});

// API路由 - 登出
app.post('/api/auth/logout', (req, res) => {
  // 登出逻辑
});
```

## 8. 部署指南

### 8.1 环境要求

- Node.js v14+
- npm 或 yarn
- 现代浏览器（支持ES6+）

### 8.2 安装步骤

1. 克隆代码库
2. 安装依赖：`npm install`
3. 配置环境变量：创建`.env`文件
4. 启动服务器：`npm start`

### 8.3 环境变量

应用需要以下环境变量：

```
# GitHub OAuth配置
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
REDIRECT_URI=http://localhost:3000/auth/github/callback

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=7d

# 服务器配置
PORT=3000
NODE_ENV=development
```

## 9. 未来计划

### 9.1 功能扩展

- 添加更多登录方式（手机号、邮箱）
- 实现用户资料管理
- 添加健康建议和文章
- 支持上传体检报告图片
- 添加提醒功能

### 9.2 技术改进

- 使用TypeScript重构代码
- 添加单元测试和集成测试
- 优化性能和加载速度
- 实现PWA（渐进式Web应用）
- 添加国际化支持

## 10. 总结

移动端体检应用是一个功能完善的健康管理工具，帮助用户更好地了解和管理自己的健康状况。通过OAuth2登录、体检报告查询、健康趋势分析和数据对比等功能，用户可以全面了解自己的健康状况，及时发现健康问题，采取相应措施改善健康。





