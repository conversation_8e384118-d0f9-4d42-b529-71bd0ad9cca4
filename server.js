require('dotenv').config();
const express = require('express');
const session = require('express-session');
const axios = require('axios');
const jwt = require('jsonwebtoken');
const path = require('path');
const httpsProxyAgent = require('https-proxy-agent');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// 会话配置
app.use(session({
  secret: process.env.JWT_SECRET || 'your_session_secret',
  resave: false,
  saveUninitialized: false,
  cookie: { secure: process.env.NODE_ENV === 'production' }
}));

// GitHub OAuth配置
const GITHUB_CLIENT_ID = process.env.GITHUB_CLIENT_ID;
const GITHUB_CLIENT_SECRET = process.env.GITHUB_CLIENT_SECRET;
const REDIRECT_URI = process.env.REDIRECT_URI || 'http://localhost:3000/auth/github/callback';

// 路由 - GitHub OAuth登录
app.get('/auth/github', (req, res) => {
  const state = req.query.state || generateRandomState();
  req.session.oauthState = state;

  const githubAuthUrl = `https://github.com/login/oauth/authorize?client_id=${GITHUB_CLIENT_ID}&redirect_uri=${REDIRECT_URI}&scope=user:email read:user&state=${state}`;
  res.redirect(githubAuthUrl);
});

// 路由 - GitHub OAuth回调
app.get('/auth/github/callback', async (req, res) => {
  const { code, state } = req.query;
  const savedState = req.session.oauthState;

  // 验证状态以防止CSRF攻击
  if (!state || state !== savedState) {
    return res.redirect('/login.html?error=invalid_state');
  }

  try {
    // 交换代码获取访问令牌
    const tokenResponse = await axios.post('https://github.com/login/oauth/access_token', {
      client_id: GITHUB_CLIENT_ID,
      client_secret: GITHUB_CLIENT_SECRET,
      code,
      redirect_uri: REDIRECT_URI
    }, {
      headers: {
        Accept: 'application/json'
      },
      timeout: 10000, // 设置10秒超时
      httpsAgent: process.env.HTTPS_PROXY ? new httpsProxyAgent(process.env.HTTPS_PROXY) : null
    });

    const accessToken = tokenResponse.data.access_token;

    // 获取用户信息
    const userResponse = await axios.get('https://api.github.com/user', {
      headers: {
        Authorization: `token ${accessToken}`
      }
    });

    const user = userResponse.data;

    // 获取用户邮箱
    const emailsResponse = await axios.get('https://api.github.com/user/emails', {
      headers: {
        Authorization: `token ${accessToken}`
      }
    });

    const emails = emailsResponse.data;
    const primaryEmail = emails.find(email => email.primary) || emails[0];

    // 创建用户对象
    const userData = {
      id: user.id,
      name: user.name || user.login,
      login: user.login,
      avatar: user.avatar_url,
      email: primaryEmail ? primaryEmail.email : null
    };

    // 创建JWT令牌
    const token = jwt.sign(userData, process.env.JWT_SECRET || 'your_jwt_secret', {
      expiresIn: process.env.JWT_EXPIRES_IN || '7d'
    });

    // 清除OAuth状态
    req.session.oauthState = null;

    // 重定向到前端，带上令牌
    res.redirect(`/callback.html?token=${token}`);
  } catch (error) {
    console.error('GitHub OAuth错误:', error);
    res.redirect('/login.html?error=github_oauth_error');
  }
});

// API路由 - 验证令牌
app.get('/api/auth/verify', (req, res) => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ valid: false, message: '未提供令牌' });
  }

  const token = authHeader.split(' ')[1];

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your_jwt_secret');
    res.json({ valid: true, user: decoded });
  } catch (error) {
    res.status(401).json({ valid: false, message: '无效的令牌' });
  }
});

// API路由 - 刷新令牌
app.post('/api/auth/refresh', (req, res) => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ success: false, message: '未提供令牌' });
  }

  const token = authHeader.split(' ')[1];

  try {
    // 验证当前令牌
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your_jwt_secret', {
      ignoreExpiration: true // 允许已过期的令牌
    });

    // 检查令牌是否已过期太久（超过7天）
    const currentTime = Math.floor(Date.now() / 1000);
    const tokenExpiry = decoded.exp;
    const maxRefreshTime = 7 * 24 * 60 * 60; // 7天

    if (currentTime - tokenExpiry > maxRefreshTime) {
      return res.status(401).json({ success: false, message: '令牌已过期太久，无法刷新' });
    }

    // 创建新令牌
    const userData = {
      id: decoded.id,
      name: decoded.name,
      login: decoded.login,
      avatar: decoded.avatar,
      email: decoded.email
    };

    const newToken = jwt.sign(userData, process.env.JWT_SECRET || 'your_jwt_secret', {
      expiresIn: process.env.JWT_EXPIRES_IN || '7d'
    });

    res.json({ success: true, token: newToken, user: userData });
  } catch (error) {
    console.error('刷新令牌错误:', error);
    res.status(401).json({ success: false, message: '无效的令牌' });
  }
});

// 生成随机状态
function generateRandomState() {
  const crypto = require('crypto');
  return crypto.randomBytes(24).toString('hex');
}

// API路由 - 登出
app.post('/api/auth/logout', (req, res) => {
  const authHeader = req.headers.authorization;

  if (authHeader && authHeader.startsWith('Bearer ')) {
    // 这里可以实现令牌黑名单，将已登出的令牌添加到黑名单中
    // 在实际生产环境中，应该使用Redis等存储黑名单
    // 简单起见，这里只返回成功响应
  }

  res.json({ success: true, message: '已成功登出' });
});

// 健康检查路由
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date() });
});

// 捕获所有其他路由，返回index.html
app.get('*', (req, res) => {
  // 排除API路由
  if (!req.path.startsWith('/api/')) {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
  } else {
    res.status(404).json({ error: 'Not found' });
  }
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({ error: '服务器内部错误' });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`服务器运行在 http://localhost:${PORT}`);
});

// 配置代理
const proxy = 'http://127.0.0.1:7890'; // 根据您的代理软件配置修改端口
const { HttpsProxyAgent } = require('https-proxy-agent');  // 使用解构赋值
axios.defaults.proxy = false;
axios.defaults.httpsAgent = new HttpsProxyAgent(proxy);  // 现在应该可以正常工作了
