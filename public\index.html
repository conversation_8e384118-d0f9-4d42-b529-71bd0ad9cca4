<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="体检应用 - 您的健康管理助手">
  <meta name="theme-color" content="#4285f4">
  <title>体检应用 - 首页</title>
  <link rel="stylesheet" href="css/styles.css">
  <link rel="stylesheet" href="css/mobile.css">
  <link rel="stylesheet" href="css/charts.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <link rel="icon" href="images/favicon.ico" type="image/x-icon">
</head>
<body>
  <!-- 头部 -->
  <header class="app-header">
    <div class="app-title">体检应用</div>
    <div class="user-info" id="user-info">
      <img id="user-avatar" class="avatar" src="images/default-avatar.png" alt="用户头像">
      <div class="user-menu" id="user-menu">
        <div class="user-menu-item">
          <i class="fas fa-user"></i>
          <span>个人资料</span>
        </div>
        <div class="user-menu-item">
          <i class="fas fa-cog"></i>
          <span>设置</span>
        </div>
        <div class="user-menu-item" id="logout-btn">
          <i class="fas fa-sign-out-alt"></i>
          <span>退出登录</span>
        </div>
      </div>
    </div>
  </header>

  <style>
   .app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.app-title {
  font-size: 1.25rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  margin-right: auto;
}

.user-info {
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 6px;
  border-radius: 24px;
  transition: all 0.3s ease;
}

.user-info:hover {
  background: rgba(255, 255, 255, 0.1);
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: transform 0.3s ease;
}

.user-info:hover .avatar {
  transform: scale(1.05);
}

.user-menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  background-color: var(--card-background);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 200px;
  z-index: 1000;
  display: none;
  overflow: hidden;
  animation: menuSlideDown 0.2s ease;
}

.user-menu.active {
  display: block;
}

@keyframes menuSlideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-menu-item {
  padding: 14px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  color: var(--text-primary);
  transition: all 0.2s ease;
  font-size: 0.95rem;
}

.user-menu-item i {
  font-size: 1.1rem;
  color: var(--text-secondary);
  width: 20px;
  text-align: center;
  transition: color 0.2s ease;
}

.user-menu-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.user-menu-item:hover i {
  color: var(--primary-color);
}

#logout-btn {
  border-top: 1px solid var(--border-color);
  color: var(--error-color);
}

#logout-btn:hover {
  background-color: rgba(234, 67, 53, 0.08);
}

#logout-btn i {
  color: var(--error-color);
}
  </style>

  <!-- 主内容 -->
  <div class="container">
    <!-- 健康评分卡片 -->
    <div class="card">
      <h2>健康评分</h2>
      <div class="health-score-container">
        <div class="score-circle">
          <div class="score-number">85</div>
          <div class="score-text">良好</div>
          <div class="score-info">
            <i class="fas fa-info-circle"></i>
            <div class="score-tooltip">
              <div class="tooltip-item">90-100：优秀</div>
              <div class="tooltip-item">80-89：良好</div>
              <div class="tooltip-item">70-79：一般</div>
              <div class="tooltip-item">60-69：较差</div>
              <div class="tooltip-item">0-59：需注意</div>
            </div>
          </div>
        </div>
        <div class="score-details">
          <div class="score-detail-item">
            <i class="fas fa-chart-line"></i>
            <div class="detail-content">
              <div class="detail-label">较上次</div>
              <div class="detail-value positive">+2</div>
            </div>
          </div>
          <div class="score-detail-item">
            <i class="fas fa-users"></i>
            <div class="detail-content">
              <div class="detail-label">同龄人群平均</div>
              <div class="detail-value">82</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近体检卡片 -->
    <div class="card">
      <div class="card-header">
        <h2>最近体检</h2>
        <a href="reports.html" class="btn-link">查看全部</a>
      </div>
      <div class="recent-report" id="recent-report">
        <div class="report-date">2023年5月15日</div>
        <div class="report-hospital">北京协和医院</div>
        <div class="report-summary">总体健康状况良好，部分指标需要注意。</div>
        <div class="critical-indicators">
          <h3>异常指标</h3>
          <div class="critical-indicator">
            <div class="critical-indicator-icon"><i class="fas fa-exclamation-triangle"></i></div>
            <div class="critical-indicator-details">
              <div class="critical-indicator-name">总胆固醇</div>
              <div class="critical-indicator-value">5.8 mmol/L</div>
              <div class="critical-indicator-range">正常范围: 0-5.2 mmol/L</div>
            </div>
          </div>
          <div class="critical-indicator">
            <div class="critical-indicator-icon"><i class="fas fa-exclamation-triangle"></i></div>
            <div class="critical-indicator-details">
              <div class="critical-indicator-name">低密度脂蛋白</div>
              <div class="critical-indicator-value">3.6 mmol/L</div>
              <div class="critical-indicator-range">正常范围: 0-3.4 mmol/L</div>
            </div>
          </div>
        </div>
        <a href="report-detail.html?id=latest" class="btn btn-primary">查看详情</a>
      </div>
    </div>

    <!-- 健康趋势卡片 -->
    <div class="card">
      <h2>健康趋势</h2>
      <div class="trend-controls">
        <div class="trend-period-selector">
          <button class="trend-period-option active">3个月</button>
          <button class="trend-period-option">6个月</button>
          <button class="trend-period-option">1年</button>
        </div>
        <div class="trend-indicator-selector">
          <div class="trend-indicator-dropdown">血糖</div>
          <div class="trend-indicator-options">
            <div class="trend-indicator-option">血糖</div>
            <div class="trend-indicator-option">总胆固醇</div>
            <div class="trend-indicator-option">血压</div>
          </div>
        </div>
      </div>
      <div class="chart-container">
        <canvas id="trend-chart"></canvas>
      </div>
      <a href="trends.html" class="btn btn-primary">查看更多趋势</a>
    </div>

    <!-- 健康建议卡片 -->
    <div class="card">
      <h2>健康建议</h2>
      <div class="health-tips">
        <div class="health-tip">
          <div class="health-tip-icon"><i class="fas fa-heartbeat"></i></div>
          <div class="health-tip-content">
            <h3>控制血脂</h3>
            <p>您的胆固醇水平偏高，建议减少高脂肪食物摄入，增加运动量。</p>
          </div>
        </div>
        <div class="health-tip">
          <div class="health-tip-icon"><i class="fas fa-apple-alt"></i></div>
          <div class="health-tip-content">
            <h3>健康饮食</h3>
            <p>增加蔬果摄入，减少精制碳水化合物，控制总热量。</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 底部导航 -->
  <nav class="bottom-nav">
    <a href="index.html" class="nav-item active">
      <i class="fas fa-home nav-icon"></i>
      <span class="nav-text">首页</span>
    </a>
    <a href="reports.html" class="nav-item">
      <i class="fas fa-clipboard-list nav-icon"></i>
      <span class="nav-text">报告</span>
    </a>
    <a href="trends.html" class="nav-item">
      <i class="fas fa-chart-line nav-icon"></i>
      <span class="nav-text">趋势</span>
    </a>
    <a href="compare.html" class="nav-item">
      <i class="fas fa-balance-scale nav-icon"></i>
      <span class="nav-text">对比</span>
    </a>
  </nav>

  <!-- 加载Chart.js库 -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="js/components/charts.js"></script>
  <script>
    // 初始化血糖趋势图
    Charts.createGlucoseTrendChart('trend-chart', 
      ['三月', '四月', '五月'],
      [5.2, 5.8, 5.5]
    );
  </script>
  <script src="js/utils/api.js"></script>
  <script src="js/utils/storage.js"></script>
  <script src="js/auth/auth.js"></script>
  
  <script src="js/pages/home.js"></script>
</body>
</html>
