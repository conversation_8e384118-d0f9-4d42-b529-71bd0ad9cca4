/* 移动端特定样式 */

/* 基础样式调整 */
body {
  font-size: 16px;
  -webkit-tap-highlight-color: transparent;
  overscroll-behavior: none;
}

.container {
  padding: 15px;
  padding-bottom: 70px; /* 为底部导航留出空间 */
}

/* 头部样式调整 */
.app-header {
  padding: 12px 15px;
}

.app-title {
  font-size: 1.1rem;
}

.back-button {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 5px;
  margin-right: 10px;
}

/* 卡片样式调整 */
.card {
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 10px;
}

/* 底部导航调整 */
.bottom-nav {
  padding: 8px 0;
  height: 60px;
}

.nav-icon {
  font-size: 1.3rem;
}

.nav-text {
  font-size: 0.7rem;
}

/* 表单元素调整 */
.form-input {
  padding: 12px;
  font-size: 16px; /* 防止iOS自动缩放 */
}

.btn {
  padding: 12px 15px;
  width: 100%;
  font-size: 1rem;
}

/* 列表项调整 */
.report-item {
  padding: 12px;
}

/* 下拉刷新样式 */
.pull-to-refresh {
  text-align: center;
  height: 50px;
  line-height: 50px;
  color: var(--text-secondary);
}

/* 滑动手势区域 */
.swipe-area {
  position: relative;
  overflow: hidden;
}

.swipe-actions {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  display: flex;
}

.swipe-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 100%;
  color: white;
}

.swipe-delete {
  background-color: var(--error-color);
}

.swipe-edit {
  background-color: var(--primary-color);
}

/* 图表容器调整 */
.chart-container {
  height: 250px;
}

/* 标签页导航 */
.tab-nav {
  display: flex;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  margin-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.tab-nav::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.tab-item {
  padding: 10px 15px;
  white-space: nowrap;
  color: var(--text-secondary);
  border-bottom: 2px solid transparent;
}

.tab-item.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

/* 浮动操作按钮 */
.fab {
  position: fixed;
  bottom: 70px;
  right: 20px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
  z-index: 90;
  font-size: 1.5rem;
}

/* 模态框 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.modal.active {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: var(--card-background);
  border-radius: 12px;
  width: 90%;
  max-width: 400px;
  max-height: 80vh;
  overflow-y: auto;
  padding: 20px;
  transform: translateY(20px);
  transition: transform 0.3s;
}

.modal.active .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.modal-title {
  font-size: 1.2rem;
  font-weight: 500;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-secondary);
}

/* 底部弹出菜单 */
.bottom-sheet {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--card-background);
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  padding: 20px;
  transform: translateY(100%);
  transition: transform 0.3s;
  z-index: 1000;
}

.bottom-sheet.active {
  transform: translateY(0);
}

.bottom-sheet-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.bottom-sheet-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* 骨架屏 */
.skeleton {
  background: linear-gradient(90deg, var(--border-color) 25%, var(--card-background) 50%, var(--border-color) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
