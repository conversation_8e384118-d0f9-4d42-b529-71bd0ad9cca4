/**
 * API工具函数
 * 用于与后端API进行通信
 */

const API = {
  /**
   * 基础URL
   */
  baseUrl: '/api',
  
  /**
   * 获取认证令牌
   * @returns {string|null} 认证令牌
   */
  getToken() {
    return localStorage.getItem('token');
  },
  
  /**
   * 设置请求头
   * @returns {Object} 请求头对象
   */
  getHeaders() {
    const headers = {
      'Content-Type': 'application/json'
    };
    
    const token = this.getToken();
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    return headers;
  },
  
  /**
   * 发送GET请求
   * @param {string} endpoint - API端点
   * @returns {Promise<any>} 响应数据
   */
  async get(endpoint) {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'GET',
        headers: this.getHeaders()
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('API GET error:', error);
      throw error;
    }
  },
  
  /**
   * 发送POST请求
   * @param {string} endpoint - API端点
   * @param {Object} data - 请求数据
   * @returns {Promise<any>} 响应数据
   */
  async post(endpoint, data) {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify(data)
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('API POST error:', error);
      throw error;
    }
  },
  
  /**
   * 发送PUT请求
   * @param {string} endpoint - API端点
   * @param {Object} data - 请求数据
   * @returns {Promise<any>} 响应数据
   */
  async put(endpoint, data) {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'PUT',
        headers: this.getHeaders(),
        body: JSON.stringify(data)
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('API PUT error:', error);
      throw error;
    }
  },
  
  /**
   * 发送DELETE请求
   * @param {string} endpoint - API端点
   * @returns {Promise<any>} 响应数据
   */
  async delete(endpoint) {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'DELETE',
        headers: this.getHeaders()
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('API DELETE error:', error);
      throw error;
    }
  },
  
  /**
   * 验证用户令牌
   * @returns {Promise<boolean>} 令牌是否有效
   */
  async verifyToken() {
    try {
      const data = await this.get('/auth/verify');
      return data.valid;
    } catch (error) {
      console.error('Token verification error:', error);
      return false;
    }
  },
  
  /**
   * 获取用户信息
   * @returns {Promise<Object>} 用户信息
   */
  async getUserInfo() {
    return this.get('/users/me');
  },
  
  /**
   * 获取健康报告列表
   * @returns {Promise<Array>} 健康报告列表
   */
  async getReports() {
    return this.get('/reports');
  },
  
  /**
   * 获取单个健康报告
   * @param {string} reportId - 报告ID
   * @returns {Promise<Object>} 健康报告
   */
  async getReport(reportId) {
    return this.get(`/reports/${reportId}`);
  },
  
  /**
   * 获取健康趋势数据
   * @param {string} indicator - 指标名称
   * @param {string} timeRange - 时间范围
   * @returns {Promise<Array>} 趋势数据
   */
  async getTrendData(indicator, timeRange) {
    return this.get(`/stats/trends?indicator=${indicator}&timeRange=${timeRange}`);
  },
  
  /**
   * 获取对比数据
   * @param {Object} params - 对比参数
   * @returns {Promise<Object>} 对比数据
   */
  async getCompareData(params) {
    const queryString = Object.entries(params)
      .map(([key, value]) => `${key}=${value}`)
      .join('&');
    
    return this.get(`/stats/compare?${queryString}`);
  },
  
  /**
   * 获取健康评分
   * @returns {Promise<Object>} 健康评分数据
   */
  async getHealthScore() {
    return this.get('/stats/health-score');
  }
};
