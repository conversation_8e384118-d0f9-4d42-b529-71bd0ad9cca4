/**
 * 图表组件
 * 用于创建和管理各种图表
 */

const Charts = {
  /**
   * 图表颜色
   */
  colors: {
    primary: '#4285f4',
    secondary: '#34a853',
    warning: '#fbbc05',
    error: '#ea4335',
    gray: '#9aa0a6',
    lightGray: '#dadce0'
  },
  
  /**
   * 创建健康评分仪表盘
   * @param {string} canvasId - Canvas元素ID
   * @param {number} score - 健康评分
   */
  createHealthScoreGauge(canvasId, score) {
    const canvas = document.getElementById(canvasId);
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // 确定颜色
    let color;
    if (score >= 80) {
      color = this.colors.secondary;
    } else if (score >= 60) {
      color = this.colors.warning;
    } else {
      color = this.colors.error;
    }
    
    // 创建仪表盘图表
    new Chart(ctx, {
      type: 'doughnut',
      data: {
        datasets: [{
          data: [score, 100 - score],
          backgroundColor: [color, this.colors.lightGray],
          borderWidth: 0
        }]
      },
      options: {
        cutout: '70%',
        circumference: 180,
        rotation: 270,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            enabled: false
          }
        },
        animation: {
          animateRotate: true,
          animateScale: true
        }
      }
    });
  },
  
  /**
   * 创建趋势图表
   * @param {string} canvasId - Canvas元素ID
   * @param {Array} data - 趋势数据
   * @param {Object} options - 配置选项
   */
  createTrendChart(canvasId, data, options = {}) {
    const canvas = document.getElementById(canvasId);
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // 默认配置
    const defaultOptions = {
      type: 'line',
      yAxisLabel: '',
      referenceMin: null,
      referenceMax: null
    };
    
    // 合并选项
    const chartOptions = { ...defaultOptions, ...options };
    
    // 提取数据
    const labels = data.map(item => item.date);
    const values = data.map(item => item.value);
    
    // 创建数据集
    const datasets = [{
      label: chartOptions.yAxisLabel,
      data: values,
      borderColor: this.colors.primary,
      backgroundColor: 'rgba(66, 133, 244, 0.1)',
      borderWidth: 2,
      tension: 0.3,
      fill: true
    }];
    
    // 添加参考线
    if (chartOptions.referenceMin !== null) {
      datasets.push({
        label: '最小参考值',
        data: Array(labels.length).fill(chartOptions.referenceMin),
        borderColor: this.colors.warning,
        borderWidth: 1,
        borderDash: [5, 5],
        pointRadius: 0,
        fill: false
      });
    }
    
    if (chartOptions.referenceMax !== null) {
      datasets.push({
        label: '最大参考值',
        data: Array(labels.length).fill(chartOptions.referenceMax),
        borderColor: this.colors.warning,
        borderWidth: 1,
        borderDash: [5, 5],
        pointRadius: 0,
        fill: false
      });
    }
    
    // 创建图表
    new Chart(ctx, {
      type: chartOptions.type,
      data: {
        labels: labels,
        datasets: datasets
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: false,
            title: {
              display: true,
              text: chartOptions.yAxisLabel
            }
          },
          x: {
            title: {
              display: true,
              text: '日期'
            }
          }
        },
        plugins: {
          legend: {
            display: true,
            position: 'top'
          },
          tooltip: {
            mode: 'index',
            intersect: false
          }
        }
      }
    });
  },
  
  /**
   * 创建对比图表
   * @param {string} canvasId - Canvas元素ID
   * @param {Object} data - 对比数据
   */
  createCompareChart(canvasId, data) {
    const canvas = document.getElementById(canvasId);
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // 提取数据
    const labels = data.categories;
    const datasets = data.datasets.map(dataset => ({
      label: dataset.label,
      data: dataset.values,
      backgroundColor: dataset.color || this.colors.primary,
      borderColor: dataset.borderColor || dataset.color || this.colors.primary,
      borderWidth: 1
    }));
    
    // 创建图表
    new Chart(ctx, {
      type: 'bar',
      data: {
        labels: labels,
        datasets: datasets
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: data.yAxisLabel || ''
            }
          }
        },
        plugins: {
          legend: {
            display: true,
            position: 'top'
          },
          tooltip: {
            mode: 'index',
            intersect: false
          }
        }
      }
    });
  },
  
  /**
   * 创建血糖趋势图表
   * @param {string} canvasId - Canvas元素ID
   * @param {Array} labels - 时间标签数组
   * @param {Array} data - 血糖数据数组 
   */
  createGlucoseTrendChart(canvasId, labels, data) {
    const ctx = document.getElementById(canvasId).getContext('2d');
    new Chart(ctx, {
      type: 'line',
      data: {
        labels: labels,
        datasets: [{
          label: '血糖 (mmol/L)',
          data: data,
          borderColor: this.colors.primary,
          backgroundColor: 'rgba(66, 133, 244, 0.1)',
          tension: 0.4,
          fill: true,
          pointRadius: 5,
          pointHoverRadius: 7
        }]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            min: 3,
            max: 7,
            ticks: {
              stepSize: 0.5
            }
          }
        }
      }
    });
  },
  
  /**
   * 创建历史趋势图表
   * @param {string} canvasId - Canvas元素ID
   * @param {Array} data - 历史数据
   * @param {Object} options - 配置选项
   */
  createHistoryChart(canvasId, data, options = {}) {
    // 使用趋势图表函数，但可以添加特定的配置
    this.createTrendChart(canvasId, data, {
      type: 'line',
      ...options
    });
  },
  
  /**
   * 创建多指标趋势图表
   * @param {string} canvasId - Canvas元素ID
   * @param {Array} labels - 时间标签数组
   * @param {Object} indicators - 指标数据对象
   */
  createMultiIndicatorChart(canvasId, labels, indicators) {
    const ctx = document.getElementById(canvasId).getContext('2d');
    const indicatorColors = [
      this.colors.primary,    // 蓝色
      '#34a853',              // 绿色
      '#ea4335',              // 红色
      '#fbbc05',              // 黄色
      '#681da8',              // 紫色
      '#00bcd4'               // 青色
    ];

    const datasets = Object.keys(indicators).map((key, index) => ({
      label: key,
      data: indicators[key],
      borderColor: indicatorColors[index],
      backgroundColor: `${indicatorColors[index]}20`,
      tension: 0.3,
      pointRadius: 3,
      borderWidth: 2
    }));

    new Chart(ctx, {
      type: 'line',
      data: {
        labels: labels,
        datasets: datasets
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            title: {
              display: true,
              text: '检测值'
            }
          },
          x: {
            title: {
              display: true,
              text: '体检日期'
            }
          }
        },
        plugins: {
          legend: {
            position: 'top',
            labels: {
              padding: 20
            }
          }
        }
      }
    });
  },
  
  createDistributionCurveChart(canvasId, userScore, distributionData) {
    const ctx = document.getElementById(canvasId).getContext('2d');
    
    // 获取用户分数对应的y值
    const userYValue = distributionData[userScore];

    new Chart(ctx, {
      type: 'line',
      data: {
        labels: distributionData.map((_,i) => i),
        datasets: [{
          label: '人群分布',
          data: distributionData,
          borderColor: this.colors.primary,
          backgroundColor: `${this.colors.primary}20`,
          tension: 0.5,
          pointRadius: 0
        }, {
          label: '您的分数',
          data: [{ 
            x: userScore,
            y: userYValue 
          }],
          pointRadius: 8,
          pointStyle: 'circle',
          backgroundColor: this.colors.warning,
          borderColor: this.colors.warning
        }]
      },
      options: {
        responsive: true,
        interaction: {
          mode: 'nearest',
          axis: 'xy',
          intersect: false
        },
        plugins: {
          crosshair: {
            line: {
              color: '#666',
              width: 1,
              dash: [5, 5]
            },
            sync: {
              enabled: false
            },
            zoom: {
              enabled: false
            }
          }
        },
        scales: {
          x: {
            type: 'linear',
            min: 0,
            max: 100,
            title: { display: true, text: '健康评分' },
            grid: { display: false }
          },
          y: {
            beginAtZero: true,
            title: { display: true, text: '人群占比 (%)' },
            ticks: { callback: v => `${v}%` }
          }
        }
      }
    });
  },
};

window.charts = Charts;  // 添加这行到文件末尾
