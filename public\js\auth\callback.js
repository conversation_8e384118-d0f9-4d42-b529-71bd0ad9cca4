/**
 * OAuth回调处理JavaScript
 */

document.addEventListener('DOMContentLoaded', () => {
  // 获取URL参数
  const urlParams = new URLSearchParams(window.location.search);
  const token = urlParams.get('token');
  
  if (!token) {
    console.error('token为空');
    window.location.href = '/login.html?error=no_token';
    return;
  }
  
  console.log('获取到的token:', token);
  
  // 保存令牌到本地存储
  localStorage.setItem('token', token);
  
  // 获取用户信息
  fetch('/api/auth/verify', {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  })
  .then(response => {
    console.log('验证响应状态:', response.status);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    console.log('验证响应数据:', data);
    if (data.valid) {
      // 保存用户信息
      localStorage.setItem('user', JSON.stringify(data.user));
      
      // 重定向到主页
      window.location.href = '/index.html';
    } else {
      console.error('服务器返回token无效');
      throw new Error('无效的令牌');
    }
  })
  .catch(error => {
    console.error('验证令牌时出错:', error.message);
    localStorage.removeItem('token');
    window.location.href = '/login.html?error=invalid_token';
  });
});
