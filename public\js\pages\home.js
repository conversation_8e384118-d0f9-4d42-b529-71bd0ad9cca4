/**
 * 首页JavaScript
 */

document.addEventListener('DOMContentLoaded', () => {
  // 检查登录状态
  if (!localStorage.isLoggedIn()) {
    window.location.href = '/login.html';
    return;
  }

  // 加载用户信息
  loadUserInfo();

  // 加载健康评分
  loadHealthScore();

  // 加载最近体检报告
  loadRecentReport();

  // 加载健康趋势
  loadHealthTrend();

  // 设置趋势控件事件
  setupTrendControls();

  // 设置用户菜单事件
  setupUserMenu();

  // 设置登出按钮事件
  setupLogoutButton();
});

/**
 * 加载用户信息
 */
async function loadUserInfo() {
  try {
    // 先从本地存储获取
    const user = Storage.getUser();

    if (user) {
      updateUserUI(user);
    }

    // 然后从API获取最新信息
    const userData = await API.getUserInfo();
    Storage.setUser(userData);
    updateUserUI(userData);
  } catch (error) {
    console.error('加载用户信息失败:', error);
  }
}

/**
 * 更新用户界面
 * @param {Object} user - 用户信息
 */
function updateUserUI(user) {
  const userAvatar = document.getElementById('user-avatar');

  if (userAvatar && user.avatar) {
    userAvatar.src = user.avatar;
  }

  // 添加用户名到菜单
  const userMenu = document.getElementById('user-menu');
  if (userMenu) {
    // 检查是否已存在用户信息项
    let userInfoItem = userMenu.querySelector('.user-info-item');

    if (!userInfoItem) {
      // 创建用户信息项
      userInfoItem = document.createElement('div');
      userInfoItem.className = 'user-menu-item user-info-item';
      userInfoItem.innerHTML = `
        <i class="fas fa-user-circle"></i>
        <div class="user-info-details">
          <div class="user-name"></div>
          <div class="user-email"></div>
        </div>
      `;

      // 添加样式
      const style = document.createElement('style');
      style.textContent = `
        .user-info-item {
          padding: 15px;
          border-bottom: 1px solid var(--border-color);
        }
        .user-info-details {
          display: flex;
          flex-direction: column;
        }
        .user-name {
          font-weight: 500;
        }
        .user-email {
          font-size: 0.8rem;
          color: var(--text-secondary);
        }
      `;
      document.head.appendChild(style);

      // 插入到菜单顶部
      userMenu.insertBefore(userInfoItem, userMenu.firstChild);
    }

    // 更新用户信息
    const userName = userInfoItem.querySelector('.user-name');
    const userEmail = userInfoItem.querySelector('.user-email');

    if (userName) {
      userName.textContent = user.name || user.login;
    }

    if (userEmail) {
      userEmail.textContent = user.email || '';
    }
  }
}

/**
 * 加载健康评分
 */
async function loadHealthScore() {
  try {
    const scoreData = await API.getHealthScore();

    // 更新评分显示
    const scoreElement = document.getElementById('health-score');
    if (scoreElement) {
      scoreElement.textContent = scoreData.score;
    }

    // 创建评分仪表盘
    Charts.createHealthScoreGauge('health-score-chart', scoreData.score);

    // 更新评分标签
    const scoreLabel = document.querySelector('.health-score-label');
    if (scoreLabel) {
      scoreLabel.textContent = getScoreLabel(scoreData.score);
    }
  } catch (error) {
    console.error('加载健康评分失败:', error);
  }
}

/**
 * 获取评分标签
 * @param {number} score - 健康评分
 * @returns {string} 评分标签
 */
function getScoreLabel(score) {
  if (score >= 90) return '优秀';
  if (score >= 80) return '良好';
  if (score >= 70) return '一般';
  if (score >= 60) return '较差';
  return '需注意';
}

/**
 * 加载最近体检报告
 */
async function loadRecentReport() {
  try {
    const reports = await API.getReports();

    if (reports && reports.length > 0) {
      // 获取最新报告
      const latestReport = reports[0];

      // 更新报告显示
      const recentReport = document.getElementById('recent-report');
      if (recentReport) {
        // 格式化日期
        const reportDate = new Date(latestReport.report_date);
        const formattedDate = reportDate.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });

        // 更新UI
        recentReport.querySelector('.report-date').textContent = formattedDate;
        recentReport.querySelector('.report-hospital').textContent = latestReport.hospital;
        recentReport.querySelector('.report-summary').textContent = latestReport.report_summary;

        // 更新异常指标
        updateCriticalIndicators(latestReport);
      }
    }
  } catch (error) {
    console.error('加载最近体检报告失败:', error);
  }
}

/**
 * 更新异常指标显示
 * @param {Object} report - 报告数据
 */
function updateCriticalIndicators(report) {
  // 这里应该根据实际API返回的数据结构进行调整
  // 示例代码仅作演示
}

/**
 * 加载健康趋势
 */
async function loadHealthTrend() {
  try {
    // 默认加载血糖趋势，3个月时间范围
    const trendData = await API.getTrendData('glucose', '3m');

    // 创建趋势图表
    Charts.createTrendChart('trend-chart', trendData, {
      yAxisLabel: '血糖 (mmol/L)',
      referenceMin: 3.9,
      referenceMax: 6.1
    });
  } catch (error) {
    console.error('加载健康趋势失败:', error);
  }
}

/**
 * 设置趋势控件事件
 */
function setupTrendControls() {
  // 时间范围选择
  const periodOptions = document.querySelectorAll('.trend-period-option');
  periodOptions.forEach(option => {
    option.addEventListener('click', async () => {
      // 移除所有活动状态
      periodOptions.forEach(opt => opt.classList.remove('active'));
      // 添加当前活动状态
      option.classList.add('active');

      // 获取当前选中的指标
      const indicator = document.querySelector('.trend-indicator-dropdown').textContent.trim();

      // 获取时间范围
      let timeRange;
      if (option.textContent === '3个月') timeRange = '3m';
      else if (option.textContent === '6个月') timeRange = '6m';
      else timeRange = '1y';

      // 重新加载趋势数据
      await updateTrendChart(indicator, timeRange);
    });
  });

  // 指标下拉菜单
  const indicatorDropdown = document.querySelector('.trend-indicator-dropdown');
  const indicatorOptions = document.querySelector('.trend-indicator-options');

  if (indicatorDropdown && indicatorOptions) {
    // 点击下拉菜单显示选项
    indicatorDropdown.addEventListener('click', () => {
      indicatorOptions.classList.toggle('active');
    });

    // 点击选项更新图表
    const options = indicatorOptions.querySelectorAll('.trend-indicator-option');
    options.forEach(option => {
      option.addEventListener('click', async () => {
        // 更新下拉菜单文本
        indicatorDropdown.textContent = option.textContent;
        // 隐藏选项
        indicatorOptions.classList.remove('active');

        // 获取当前选中的时间范围
        const activeOption = document.querySelector('.trend-period-option.active');
        let timeRange = '3m';
        if (activeOption) {
          if (activeOption.textContent === '6个月') timeRange = '6m';
          else if (activeOption.textContent === '1年') timeRange = '1y';
        }

        // 重新加载趋势数据
        await updateTrendChart(option.textContent, timeRange);
      });
    });

    // 点击其他地方关闭下拉菜单
    document.addEventListener('click', (event) => {
      if (!event.target.closest('.trend-indicator-selector')) {
        indicatorOptions.classList.remove('active');
      }
    });
  }
}

/**
 * 更新趋势图表
 * @param {string} indicator - 指标名称
 * @param {string} timeRange - 时间范围
 */
async function updateTrendChart(indicator, timeRange) {
  try {
    // 将中文指标名称转换为API参数
    let apiIndicator;
    let yAxisLabel;
    let referenceMin = null;
    let referenceMax = null;

    switch (indicator) {
      case '血糖':
        apiIndicator = 'glucose';
        yAxisLabel = '血糖 (mmol/L)';
        referenceMin = 3.9;
        referenceMax = 6.1;
        break;
      case '总胆固醇':
        apiIndicator = 'cholesterol';
        yAxisLabel = '总胆固醇 (mmol/L)';
        referenceMax = 5.2;
        break;
      case '血压':
        apiIndicator = 'blood_pressure';
        yAxisLabel = '血压 (mmHg)';
        break;
      default:
        apiIndicator = 'glucose';
        yAxisLabel = '血糖 (mmol/L)';
        referenceMin = 3.9;
        referenceMax = 6.1;
    }

    // 加载趋势数据
    const trendData = await API.getTrendData(apiIndicator, timeRange);

    // 更新图表
    Charts.createTrendChart('trend-chart', trendData, {
      yAxisLabel,
      referenceMin,
      referenceMax
    });
  } catch (error) {
    console.error('更新趋势图表失败:', error);
  }
}

/**
 * 设置用户菜单事件
 */
function setupUserMenu() {
  const userInfo = document.getElementById('user-info');
  const userMenu = document.getElementById('user-menu');

  if (userInfo && userMenu) {
    // 点击用户头像显示菜单
    userInfo.addEventListener('click', (event) => {
      event.stopPropagation();
      userMenu.classList.toggle('active');
    });

    // 点击其他地方关闭菜单
    document.addEventListener('click', (event) => {
      if (!userInfo.contains(event.target)) {
        userMenu.classList.remove('active');
      }
    });

    // 阻止菜单内部点击事件冒泡
    userMenu.addEventListener('click', (event) => {
      event.stopPropagation();
    });
  }
}

/**
 * 设置登出按钮事件
 */
function setupLogoutButton() {
  const logoutBtn = document.getElementById('logout-btn');

  if (logoutBtn) {
    logoutBtn.addEventListener('click', () => {
      // 显示确认对话框
      showLogoutConfirmation();
    });
  }
}

/**
 * 显示登出确认对话框
 */
function showLogoutConfirmation() {
  // 创建确认对话框
  const modal = document.createElement('div');
  modal.className = 'modal';
  modal.id = 'logout-confirmation';

  modal.innerHTML = `
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title">确认登出</h3>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <p>您确定要退出登录吗？</p>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary modal-close">取消</button>
        <button class="btn btn-error" id="confirm-logout-btn">确认登出</button>
      </div>
    </div>
  `;

  // 添加到文档
  document.body.appendChild(modal);

  // 添加样式
  const style = document.createElement('style');
  style.textContent = `
    .modal {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s;
    }
    .modal.active {
      opacity: 1;
      visibility: visible;
    }
    .modal-content {
      background-color: var(--card-background);
      border-radius: 12px;
      width: 90%;
      max-width: 400px;
      max-height: 80vh;
      overflow-y: auto;
      padding: 20px;
      transform: translateY(20px);
      transition: transform 0.3s;
    }
    .modal.active .modal-content {
      transform: translateY(0);
    }
    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }
    .modal-title {
      font-size: 1.2rem;
      font-weight: 500;
    }
    .modal-close {
      background: none;
      border: none;
      font-size: 1.5rem;
      cursor: pointer;
      color: var(--text-secondary);
    }
    .modal-footer {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      margin-top: 20px;
    }
  `;
  document.head.appendChild(style);

  // 显示模态框
  setTimeout(() => {
    modal.classList.add('active');
  }, 10);

  // 关闭按钮事件
  const closeButtons = modal.querySelectorAll('.modal-close');
  closeButtons.forEach(button => {
    button.addEventListener('click', () => {
      modal.classList.remove('active');
      setTimeout(() => {
        modal.remove();
      }, 300);
    });
  });

  // 确认登出按钮事件
  const confirmBtn = document.getElementById('confirm-logout-btn');
  if (confirmBtn) {
    confirmBtn.addEventListener('click', () => {
      // 调用Auth模块的登出方法
      if (typeof Auth !== 'undefined') {
        Auth.logout(true);
      } else {
        // 如果Auth模块不可用，使用简单的登出方法
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        window.location.href = '/login.html?logout=true';
      }
    });
  }
}
