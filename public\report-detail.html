<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="体检应用 - 您的健康管理助手">
  <meta name="theme-color" content="#4285f4">
  <title>体检应用 - 报告详情</title>
  <link rel="stylesheet" href="css/styles.css">
  <link rel="stylesheet" href="css/mobile.css">
  <link rel="stylesheet" href="css/charts.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <link rel="icon" href="images/favicon.ico" type="image/x-icon">
  <style>
    .report-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 20px;
      text-align: center;
    }
    
    .report-date {
      font-size: 1.2rem;
      font-weight: 500;
      margin-bottom: 5px;
    }
    
    .report-hospital {
      color: var(--text-secondary);
    }
    
    .report-score-container {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 15px 0;
    }
    
    .report-score {
      font-size: 2rem;
      font-weight: bold;
      margin-right: 10px;
    }
    
    .report-score-label {
      padding: 3px 10px;
      border-radius: 15px;
      color: white;
      font-weight: 500;
    }
    
    .report-summary {
      padding: 15px;
      background-color: rgba(66, 133, 244, 0.1);
      border-radius: 8px;
      margin-bottom: 20px;
    }
    
    .category-tabs {
      display: flex;
      overflow-x: auto;
      margin-bottom: 20px;
      border-bottom: 1px solid var(--border-color);
    }
    
    .category-tab {
      padding: 10px 15px;
      white-space: nowrap;
      color: var(--text-secondary);
      border-bottom: 2px solid transparent;
      cursor: pointer;
    }
    
    .category-tab.active {
      color: var(--primary-color);
      border-bottom-color: var(--primary-color);
    }
    
    .indicators-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    
    .indicators-table th,
    .indicators-table td {
      padding: 12px 8px;
      text-align: left;
      border-bottom: 1px solid var(--border-color);
    }
    
    .indicators-table th {
      color: var(--text-secondary);
      font-weight: 500;
      font-size: 0.9rem;
    }
    
    .indicator-value {
      font-weight: 500;
    }
    
    .indicator-range {
      font-size: 0.8rem;
      color: var(--text-secondary);
    }
    
    .action-buttons {
      display: flex;
      gap: 10px;
      margin-top: 20px;
    }
    
    .action-buttons .btn {
      flex: 1;
    }
  </style>
</head>
<body>
  <!-- 头部 -->
  <header class="app-header">
    <button class="back-button" onclick="history.back()">
      <i class="fas fa-arrow-left"></i>
    </button>
    <div class="app-title">报告详情</div>
    <div class="user-info">
      <img id="user-avatar" class="avatar" src="images/default-avatar.png" alt="用户头像">
    </div>
  </header>

  <!-- 主内容 -->
  <div class="container">
    <div class="card">
      <!-- 报告头部信息 -->
      <div class="report-header">
        <div class="report-date">2023年5月15日</div>
        <div class="report-hospital">北京协和医院</div>
        <div class="report-score-container">
          <div class="report-score">85</div>
          <div class="report-score-label score-good">良好</div>
        </div>
      </div>
      
      <!-- 报告摘要 -->
      <div class="report-summary">
        <h3>报告摘要</h3>
        <p>总体健康状况良好，部分指标需要注意。建议控制饮食，增加运动，定期复查。</p>
      </div>
      
      <!-- 分类标签页 -->
      <div class="category-tabs">
        <div class="category-tab active" data-category="blood">血液检查</div>
        <div class="category-tab" data-category="liver">肝功能</div>
        <div class="category-tab" data-category="kidney">肾功能</div>
        <div class="category-tab" data-category="heart">心电图</div>
      </div>
      
      <!-- 指标表格 -->
      <div class="category-content" id="blood-content">
        <table class="indicators-table">
          <thead>
            <tr>
              <th>指标名称</th>
              <th>检测值</th>
              <th>参考范围</th>
              <th>状态</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>白细胞</td>
              <td>
                <div class="indicator-value">6.5 × 10^9/L</div>
              </td>
              <td>
                <div class="indicator-range">4.0-10.0 × 10^9/L</div>
              </td>
              <td><span class="indicator-normal">正常</span></td>
            </tr>
            <tr>
              <td>红细胞</td>
              <td>
                <div class="indicator-value">4.8 × 10^12/L</div>
              </td>
              <td>
                <div class="indicator-range">4.0-5.5 × 10^12/L</div>
              </td>
              <td><span class="indicator-normal">正常</span></td>
            </tr>
            <tr>
              <td>血红蛋白</td>
              <td>
                <div class="indicator-value">145 g/L</div>
              </td>
              <td>
                <div class="indicator-range">120-160 g/L</div>
              </td>
              <td><span class="indicator-normal">正常</span></td>
            </tr>
            <tr>
              <td>总胆固醇</td>
              <td>
                <div class="indicator-value">5.8 mmol/L</div>
              </td>
              <td>
                <div class="indicator-range">0-5.2 mmol/L</div>
              </td>
              <td><span class="indicator-critical">偏高</span></td>
            </tr>
            <tr>
              <td>低密度脂蛋白</td>
              <td>
                <div class="indicator-value">3.6 mmol/L</div>
              </td>
              <td>
                <div class="indicator-range">0-3.4 mmol/L</div>
              </td>
              <td><span class="indicator-critical">偏高</span></td>
            </tr>
            <tr>
              <td>血糖</td>
              <td>
                <div class="indicator-value">5.8 mmol/L</div>
              </td>
              <td>
                <div class="indicator-range">3.9-6.1 mmol/L</div>
              </td>
              <td><span class="indicator-normal">正常</span></td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- 操作按钮 -->
      <div class="action-buttons">
        <button class="btn btn-primary">
          <i class="fas fa-share-alt"></i> 分享报告
        </button>
        <button class="btn btn-secondary">
          <i class="fas fa-download"></i> 下载PDF
        </button>
      </div>
    </div>
    
    <!-- 异常指标分析 -->
    <div class="card">
      <h2>异常指标分析</h2>
      <div class="critical-indicators">
        <div class="critical-indicator">
          <div class="critical-indicator-icon"><i class="fas fa-exclamation-triangle"></i></div>
          <div class="critical-indicator-details">
            <div class="critical-indicator-name">总胆固醇</div>
            <div class="critical-indicator-value">5.8 mmol/L <span class="indicator-critical">偏高</span></div>
            <div class="critical-indicator-range">正常范围: 0-5.2 mmol/L</div>
            <div class="critical-indicator-analysis">
              <p>胆固醇偏高可能增加心血管疾病风险。建议减少高脂肪食物摄入，增加运动量，必要时咨询医生是否需要药物治疗。</p>
            </div>
          </div>
        </div>
        <div class="critical-indicator">
          <div class="critical-indicator-icon"><i class="fas fa-exclamation-triangle"></i></div>
          <div class="critical-indicator-details">
            <div class="critical-indicator-name">低密度脂蛋白</div>
            <div class="critical-indicator-value">3.6 mmol/L <span class="indicator-critical">偏高</span></div>
            <div class="critical-indicator-range">正常范围: 0-3.4 mmol/L</div>
            <div class="critical-indicator-analysis">
              <p>低密度脂蛋白偏高会增加动脉粥样硬化风险。建议减少饱和脂肪和反式脂肪的摄入，增加膳食纤维摄入。</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 历史趋势 -->
    <div class="card">
      <h2>历史趋势</h2>
      <div class="chart-container">
        <canvas id="history-chart"></canvas>
      </div>
      
    </div>
  </div>

  <!-- 底部导航 -->
  <nav class="bottom-nav">
    <a href="index.html" class="nav-item">
      <i class="fas fa-home nav-icon"></i>
      <span class="nav-text">首页</span>
    </a>
    <a href="reports.html" class="nav-item active">
      <i class="fas fa-clipboard-list nav-icon"></i>
      <span class="nav-text">报告</span>
    </a>
    <a href="trends.html" class="nav-item">
      <i class="fas fa-chart-line nav-icon"></i>
      <span class="nav-text">趋势</span>
    </a>
    <a href="compare.html" class="nav-item">
      <i class="fas fa-balance-scale nav-icon"></i>
      <span class="nav-text">对比</span>
    </a>
  </nav>

  <!-- 加载Chart.js库 -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="js/utils/api.js"></script>
  <script src="js/utils/storage.js"></script>
  <script src="js/components/charts.js"></script>
  <script>
    // 初始化历史趋势图（示例数据）
    const historyDates = ['2023-03-15', '2023-05-15', '2023-07-15'];
    const indicatorData = {
      '白细胞': [6.2, 6.5, 6.3],
      '红细胞': [4.5, 4.8, 4.7],
      '血红蛋白': [135, 145, 140],
      '总胆固醇': [5.2, 5.8, 5.6],
      '低密度脂蛋白': [3.2, 3.6, 3.4],
      '血糖': [5.3, 5.8, 5.5]
    };
    
    Charts.createMultiIndicatorChart('history-chart', historyDates, indicatorData);
  </script>
  
</body>
</html>
