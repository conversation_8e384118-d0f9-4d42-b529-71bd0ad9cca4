/**
 * 存储工具函数
 * 用于管理本地存储
 */

const Storage = {
  /**
   * 存储前缀
   */
  prefix: 'health_app_',
  
  /**
   * 设置存储项
   * @param {string} key - 键名
   * @param {any} value - 值
   */
  set(key, value) {
    const prefixedKey = this.prefix + key;
    const stringValue = typeof value === 'object' ? JSON.stringify(value) : String(value);
    
    try {
      localStorage.setItem(prefixedKey, stringValue);
    } catch (error) {
      console.error('Storage set error:', error);
    }
  },
  
  /**
   * 获取存储项
   * @param {string} key - 键名
   * @param {boolean} [parse=true] - 是否解析JSON
   * @returns {any} 存储值
   */
  get(key, parse = true) {
    const prefixedKey = this.prefix + key;
    const value = localStorage.getItem(prefixedKey);
    
    if (value === null) {
      return null;
    }
    
    if (parse) {
      try {
        return JSON.parse(value);
      } catch (error) {
        // 如果不是JSON格式，直接返回字符串
        return value;
      }
    }
    
    return value;
  },
  
  /**
   * 删除存储项
   * @param {string} key - 键名
   */
  remove(key) {
    const prefixedKey = this.prefix + key;
    localStorage.removeItem(prefixedKey);
  },
  
  /**
   * 清除所有存储项
   */
  clear() {
    const keys = Object.keys(localStorage);
    
    for (const key of keys) {
      if (key.startsWith(this.prefix)) {
        localStorage.removeItem(key);
      }
    }
  },
  
  /**
   * 获取用户信息
   * @returns {Object|null} 用户信息
   */
  getUser() {
    return this.get('user');
  },
  
  /**
   * 设置用户信息
   * @param {Object} user - 用户信息
   */
  setUser(user) {
    this.set('user', user);
  },
  
  /**
   * 获取认证令牌
   * @returns {string|null} 认证令牌
   */
  getToken() {
    return localStorage.getItem('token');
  },
  
  /**
   * 设置认证令牌
   * @param {string} token - 认证令牌
   */
  setToken(token) {
    localStorage.setItem('token', token);
  },
  
  /**
   * 清除认证信息
   */
  clearAuth() {
    localStorage.removeItem('token');
    this.remove('user');
  },
  
  /**
   * 检查是否已登录
   * @returns {boolean} 是否已登录
   */
  isLoggedIn() {
    return !!this.getToken();
  },
  
  /**
   * 缓存报告数据
   * @param {Array} reports - 报告数据
   */
  cacheReports(reports) {
    this.set('reports_cache', {
      data: reports,
      timestamp: Date.now()
    });
  },
  
  /**
   * 获取缓存的报告数据
   * @param {number} maxAge - 最大缓存时间（毫秒）
   * @returns {Array|null} 报告数据
   */
  getCachedReports(maxAge = 5 * 60 * 1000) { // 默认5分钟
    const cache = this.get('reports_cache');
    
    if (!cache) {
      return null;
    }
    
    const now = Date.now();
    const age = now - cache.timestamp;
    
    if (age > maxAge) {
      // 缓存过期
      this.remove('reports_cache');
      return null;
    }
    
    return cache.data;
  }
};
