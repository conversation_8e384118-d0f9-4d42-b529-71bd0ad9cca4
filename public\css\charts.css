/* 图表相关样式 */

.chart-container {
  position: relative;
  width: 100%;
  height: 300px;
  margin: 20px 0;
  position: relative;
}

#trend-chart {
  background: var(--card-background);
  border-radius: 8px;
  padding: 15px;
}

.chart-title {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 10px;
  color: var(--text-primary);
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: 15px;
  margin-bottom: 5px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 5px;
}

.legend-text {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

/* 健康评分仪表盘 */
.health-score-container {
  position: relative;
  width: 100%;
  max-width: 300px;
  margin: 0 auto;
  text-align: center;
}

.health-score-gauge {
  position: relative;
  width: 100%;
  padding-bottom: 50%; /* 半圆高宽比 */
}

.health-score-value {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  font-size: 2.5rem;
  font-weight: bold;
}

.health-score-label {
  margin-top: 10px;
  font-size: 1rem;
  color: var(--text-secondary);
}

/* 趋势图 */
.trend-chart {
  height: 250px;
}

.trend-controls {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.trend-period-selector {
  display: flex;
  border: 1px solid var(--border-color);
  border-radius: 20px;
  overflow: hidden;
}

.trend-period-option {
  padding: 5px 10px;
  font-size: 0.8rem;
  background: none;
  border: none;
  cursor: pointer;
}

.trend-period-option.active {
  background-color: var(--primary-color);
  color: white;
}

.trend-indicator-selector {
  position: relative;
}

.trend-indicator-dropdown {
  padding: 5px 10px;
  border: 1px solid var(--border-color);
  border-radius: 20px;
  font-size: 0.8rem;
  background-color: var(--card-background);
  cursor: pointer;
  display: flex;
  align-items: center;
}

.trend-indicator-dropdown:after {
  content: '▼';
  margin-left: 5px;
  font-size: 0.7rem;
}

.trend-indicator-options {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: 0 2px 10px var(--shadow-color);
  z-index: 10;
  min-width: 150px;
  display: none;
}

.trend-indicator-options.active {
  display: block;
}

.trend-indicator-option {
  padding: 8px 12px;
  cursor: pointer;
}

.trend-indicator-option:hover {
  background-color: var(--background-light);
}

/* 对比图 */
.compare-chart {
  height: 300px;
}

.compare-controls {
  margin-bottom: 15px;
}

.compare-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 10px;
}

.compare-option {
  flex: 1;
  min-width: 120px;
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  text-align: center;
  cursor: pointer;
}

.compare-option.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* 异常指标高亮 */
.critical-indicators {
  margin-top: 20px;
}

.critical-indicator {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: rgba(234, 67, 53, 0.1);
  border-left: 3px solid var(--critical-color);
  border-radius: 4px;
  margin-bottom: 10px;
}

.critical-indicator-icon {
  color: var(--critical-color);
  font-size: 1.2rem;
  margin-right: 10px;
}

.critical-indicator-details {
  flex: 1;
}

.critical-indicator-name {
  font-weight: 500;
}

.critical-indicator-value {
  color: var(--critical-color);
  font-weight: bold;
}

.critical-indicator-range {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

/* 响应式调整 */
@media (max-width: 480px) {
  .chart-container {
    height: 250px;
  }
  
  .health-score-value {
    font-size: 2rem;
  }
  
  .trend-controls {
    flex-direction: column;
    gap: 10px;
  }
  
  .trend-period-selector {
    justify-content: center;
  }
  
  .trend-indicator-selector {
    align-self: center;
  }
}
