<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="体检应用 - 您的健康管理助手">
  <meta name="theme-color" content="#4285f4">
  <title>体检应用 - 体检报告</title>
  <link rel="stylesheet" href="css/styles.css">
  <link rel="stylesheet" href="css/mobile.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <link rel="icon" href="images/favicon.ico" type="image/x-icon">
  <style>
    .timeline {
      margin-top: 20px;
    }
    
    .year-divider {
      margin: 20px 0 10px;
      font-weight: 500;
      color: var(--text-secondary);
      border-bottom: 1px solid var(--border-color);
      padding-bottom: 5px;
    }
    
    .report-card {
      background-color: var(--card-background);
      border-radius: 10px;
      padding: 15px;
      margin-bottom: 15px;
      box-shadow: 0 2px 5px var(--shadow-color);
      position: relative;
    }
    
    .report-card:before {
      content: '';
      position: absolute;
      left: -20px;
      top: 50%;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: var(--primary-color);
      transform: translateY(-50%);
    }
    
    .report-card:after {
      content: '';
      position: absolute;
      left: -16px;
      top: 50%;
      width: 16px;
      height: 2px;
      background-color: var(--primary-color);
      transform: translateY(-50%);
    }
    
    .report-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
    
    .report-date {
      font-weight: 500;
    }
    
    .report-score {
      font-weight: bold;
      padding: 3px 8px;
      border-radius: 15px;
      color: white;
      font-size: 0.8rem;
    }
    
    .report-hospital {
      color: var(--text-secondary);
      font-size: 0.9rem;
      margin-bottom: 5px;
    }
    
    .report-summary {
      margin-bottom: 10px;
    }
    
    .report-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      margin-top: 10px;
    }
    
    .report-tag {
      padding: 3px 8px;
      border-radius: 15px;
      font-size: 0.8rem;
      background-color: rgba(66, 133, 244, 0.1);
      color: var(--primary-color);
    }
    
    .report-tag.critical {
      background-color: rgba(234, 67, 53, 0.1);
      color: var(--error-color);
    }
    
    .search-bar {
      display: flex;
      margin-bottom: 20px;
    }
    
    .search-input {
      flex: 1;
      padding: 10px 15px;
      border: 1px solid var(--border-color);
      border-radius: 20px;
      font-size: 1rem;
    }
    
    .filter-button {
      background: none;
      border: none;
      padding: 0 10px;
      font-size: 1.2rem;
      color: var(--text-secondary);
    }
  </style>
</head>
<body>
  <!-- 头部 -->
  <header class="app-header">
    <div class="app-title">体检报告</div>
    <div class="user-info">
      <img id="user-avatar" class="avatar" src="images/default-avatar.png" alt="用户头像">
    </div>
  </header>

  <!-- 主内容 -->
  <div class="container">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <input type="text" class="search-input" placeholder="搜索报告或指标...">
      <button class="filter-button"><i class="fas fa-filter"></i></button>
    </div>
    
    <!-- 时间轴报告列表 -->
    <div class="timeline" id="reports-timeline">
      <div class="year-divider">2023年</div>
      
      <div class="report-card">
        <div class="report-header">
          <div class="report-date">2023年5月15日</div>
          <div class="report-score score-good">85分</div>
        </div>
        <div class="report-hospital">北京协和医院</div>
        <div class="report-summary">总体健康状况良好，部分指标需要注意。</div>
        <div class="report-tags">
          <div class="report-tag critical">总胆固醇偏高</div>
          <div class="report-tag critical">低密度脂蛋白偏高</div>
          <div class="report-tag">血压正常</div>
        </div>
        <a href="report-detail.html?id=1" class="btn btn-primary" style="margin-top: 10px;">查看详情</a>
      </div>
      
      <div class="report-card">
        <div class="report-header">
          <div class="report-date">2023年2月8日</div>
          <div class="report-score score-warning">75分</div>
        </div>
        <div class="report-hospital">北京朝阳医院</div>
        <div class="report-summary">部分指标异常，需要进一步关注。</div>
        <div class="report-tags">
          <div class="report-tag critical">总胆固醇偏高</div>
          <div class="report-tag critical">血糖偏高</div>
          <div class="report-tag critical">肝功能异常</div>
        </div>
        <a href="report-detail.html?id=2" class="btn btn-primary" style="margin-top: 10px;">查看详情</a>
      </div>
      
      <div class="year-divider">2022年</div>
      
      <div class="report-card">
        <div class="report-header">
          <div class="report-date">2022年11月20日</div>
          <div class="report-score score-good">88分</div>
        </div>
        <div class="report-hospital">北京协和医院</div>
        <div class="report-summary">总体健康状况良好。</div>
        <div class="report-tags">
          <div class="report-tag">血压正常</div>
          <div class="report-tag">血糖正常</div>
          <div class="report-tag">肝功能正常</div>
        </div>
        <a href="report-detail.html?id=3" class="btn btn-primary" style="margin-top: 10px;">查看详情</a>
      </div>
    </div>
  </div>

  <!-- 底部导航 -->
  <nav class="bottom-nav">
    <a href="index.html" class="nav-item">
      <i class="fas fa-home nav-icon"></i>
      <span class="nav-text">首页</span>
    </a>
    <a href="reports.html" class="nav-item active">
      <i class="fas fa-clipboard-list nav-icon"></i>
      <span class="nav-text">报告</span>
    </a>
    <a href="trends.html" class="nav-item">
      <i class="fas fa-chart-line nav-icon"></i>
      <span class="nav-text">趋势</span>
    </a>
    <a href="compare.html" class="nav-item">
      <i class="fas fa-balance-scale nav-icon"></i>
      <span class="nav-text">对比</span>
    </a>
  </nav>

  <!-- 浮动操作按钮 -->
  <a href="#" class="fab" id="add-report-btn">
    <i class="fas fa-plus"></i>
  </a>

  <script src="js/utils/api.js"></script>
  <script src="js/utils/storage.js"></script>
  <script src="js/pages/reports.js"></script>
</body>
</html>
