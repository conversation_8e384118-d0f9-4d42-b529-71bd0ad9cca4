-- 数据库性能优化SQL脚本
-- 包含索引优化、查询优化、视图创建等

-- =============================================
-- 索引优化
-- =============================================

-- 用户表索引
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- 体检报告表索引
CREATE INDEX IF NOT EXISTS idx_health_reports_user_id ON health_reports(user_id);
CREATE INDEX IF NOT EXISTS idx_health_reports_date ON health_reports(report_date);
CREATE INDEX IF NOT EXISTS idx_health_reports_user_date ON health_reports(user_id, report_date DESC);
CREATE INDEX IF NOT EXISTS idx_health_reports_score ON health_reports(health_score);

-- 血液检查表索引
CREATE INDEX IF NOT EXISTS idx_blood_tests_report_id ON blood_tests(report_id);
CREATE INDEX IF NOT EXISTS idx_blood_tests_glucose ON blood_tests(glucose);
CREATE INDEX IF NOT EXISTS idx_blood_tests_cholesterol ON blood_tests(cholesterol);

-- 肝功能检查表索引
CREATE INDEX IF NOT EXISTS idx_liver_tests_report_id ON liver_function_tests(report_id);
CREATE INDEX IF NOT EXISTS idx_liver_tests_alt ON liver_function_tests(alt);
CREATE INDEX IF NOT EXISTS idx_liver_tests_ast ON liver_function_tests(ast);

-- 肾功能检查表索引
CREATE INDEX IF NOT EXISTS idx_kidney_tests_report_id ON kidney_function_tests(report_id);
CREATE INDEX IF NOT EXISTS idx_kidney_tests_cre ON kidney_function_tests(cre);

-- 心电图检查表索引
CREATE INDEX IF NOT EXISTS idx_ecg_tests_report_id ON ecg_tests(report_id);
CREATE INDEX IF NOT EXISTS idx_ecg_tests_heart_rate ON ecg_tests(heart_rate);

-- 字段映射表索引
CREATE INDEX IF NOT EXISTS idx_field_mappings_category ON field_mappings(category);
CREATE INDEX IF NOT EXISTS idx_field_mappings_field_name ON field_mappings(field_name);

-- =============================================
-- 视图创建 - 简化复杂查询
-- =============================================

-- 用户最新体检报告视图
CREATE OR REPLACE VIEW user_latest_reports AS
SELECT 
    u.id as user_id,
    u.name as user_name,
    u.email,
    hr.id as report_id,
    hr.report_date,
    hr.hospital,
    hr.health_score,
    hr.report_summary
FROM users u
JOIN health_reports hr ON u.id = hr.user_id
WHERE hr.report_date = (
    SELECT MAX(report_date) 
    FROM health_reports 
    WHERE user_id = u.id
);

-- 完整体检数据视图
CREATE OR REPLACE VIEW complete_health_data AS
SELECT 
    hr.id as report_id,
    hr.user_id,
    hr.report_date,
    hr.hospital,
    hr.health_score,
    -- 血液检查数据
    bt.wbc,
    bt.rbc,
    bt.hgb,
    bt.plt,
    bt.cholesterol,
    bt.triglycerides,
    bt.hdl,
    bt.ldl,
    bt.glucose,
    -- 肝功能数据
    lt.alt,
    lt.ast,
    lt.alp,
    lt.ggt,
    lt.tbil,
    lt.dbil,
    lt.tp,
    lt.alb,
    -- 肾功能数据
    kt.bun,
    kt.cre,
    kt.ua,
    -- 心电图数据
    et.heart_rate,
    et.pr_interval,
    et.qrs_duration,
    et.qt_interval
FROM health_reports hr
LEFT JOIN blood_tests bt ON hr.id = bt.report_id
LEFT JOIN liver_function_tests lt ON hr.id = lt.report_id
LEFT JOIN kidney_function_tests kt ON hr.id = kt.report_id
LEFT JOIN ecg_tests et ON hr.id = et.report_id;

-- 异常指标统计视图
CREATE OR REPLACE VIEW abnormal_indicators AS
SELECT 
    hr.user_id,
    hr.report_date,
    hr.id as report_id,
    -- 血液异常指标
    CASE WHEN bt.glucose > 6.1 OR bt.glucose < 3.9 THEN 'glucose' END as abnormal_glucose,
    CASE WHEN bt.cholesterol > 5.2 THEN 'cholesterol' END as abnormal_cholesterol,
    CASE WHEN bt.ldl > 3.4 THEN 'ldl' END as abnormal_ldl,
    CASE WHEN bt.hdl < 1.0 THEN 'hdl' END as abnormal_hdl,
    -- 肝功能异常指标
    CASE WHEN lt.alt > 40 THEN 'alt' END as abnormal_alt,
    CASE WHEN lt.ast > 40 THEN 'ast' END as abnormal_ast,
    CASE WHEN lt.tbil > 21 THEN 'tbil' END as abnormal_tbil,
    -- 肾功能异常指标
    CASE WHEN kt.cre > 133 OR kt.cre < 44 THEN 'cre' END as abnormal_cre,
    CASE WHEN kt.bun > 8.2 OR kt.bun < 2.9 THEN 'bun' END as abnormal_bun,
    -- 心电图异常指标
    CASE WHEN et.heart_rate > 100 OR et.heart_rate < 60 THEN 'heart_rate' END as abnormal_heart_rate
FROM health_reports hr
LEFT JOIN blood_tests bt ON hr.id = bt.report_id
LEFT JOIN liver_function_tests lt ON hr.id = lt.report_id
LEFT JOIN kidney_function_tests kt ON hr.id = kt.report_id
LEFT JOIN ecg_tests et ON hr.id = et.report_id;

-- 健康趋势分析视图
CREATE OR REPLACE VIEW health_trends AS
SELECT 
    user_id,
    report_date,
    glucose,
    cholesterol,
    ldl,
    hdl,
    alt,
    ast,
    cre,
    heart_rate,
    health_score,
    LAG(glucose) OVER (PARTITION BY user_id ORDER BY report_date) as prev_glucose,
    LAG(cholesterol) OVER (PARTITION BY user_id ORDER BY report_date) as prev_cholesterol,
    LAG(health_score) OVER (PARTITION BY user_id ORDER BY report_date) as prev_health_score
FROM complete_health_data
ORDER BY user_id, report_date;

-- =============================================
-- 存储过程 - 提高查询性能
-- =============================================

-- 获取用户健康趋势数据
DELIMITER //
CREATE PROCEDURE GetUserHealthTrends(
    IN p_user_id VARCHAR(50),
    IN p_indicator VARCHAR(50),
    IN p_months INT
)
BEGIN
    DECLARE sql_query TEXT;
    
    SET sql_query = CONCAT(
        'SELECT report_date, ', p_indicator, ' as value ',
        'FROM complete_health_data ',
        'WHERE user_id = ? ',
        'AND report_date >= DATE_SUB(CURDATE(), INTERVAL ? MONTH) ',
        'ORDER BY report_date'
    );
    
    SET @sql = sql_query;
    SET @user_id = p_user_id;
    SET @months = p_months;
    
    PREPARE stmt FROM @sql;
    EXECUTE stmt USING @user_id, @months;
    DEALLOCATE PREPARE stmt;
END //
DELIMITER ;

-- 计算健康评分
DELIMITER //
CREATE PROCEDURE CalculateHealthScore(
    IN p_report_id VARCHAR(36),
    OUT p_health_score INT
)
BEGIN
    DECLARE v_score INT DEFAULT 100;
    DECLARE v_glucose FLOAT;
    DECLARE v_cholesterol FLOAT;
    DECLARE v_alt FLOAT;
    DECLARE v_cre FLOAT;
    
    -- 获取检查数据
    SELECT bt.glucose, bt.cholesterol, lt.alt, kt.cre
    INTO v_glucose, v_cholesterol, v_alt, v_cre
    FROM health_reports hr
    LEFT JOIN blood_tests bt ON hr.id = bt.report_id
    LEFT JOIN liver_function_tests lt ON hr.id = lt.report_id
    LEFT JOIN kidney_function_tests kt ON hr.id = kt.report_id
    WHERE hr.id = p_report_id;
    
    -- 血糖评分
    IF v_glucose IS NOT NULL THEN
        IF v_glucose > 6.1 OR v_glucose < 3.9 THEN
            SET v_score = v_score - 10;
        END IF;
    END IF;
    
    -- 胆固醇评分
    IF v_cholesterol IS NOT NULL THEN
        IF v_cholesterol > 5.2 THEN
            SET v_score = v_score - 15;
        END IF;
    END IF;
    
    -- 肝功能评分
    IF v_alt IS NOT NULL THEN
        IF v_alt > 40 THEN
            SET v_score = v_score - 10;
        END IF;
    END IF;
    
    -- 肾功能评分
    IF v_cre IS NOT NULL THEN
        IF v_cre > 133 OR v_cre < 44 THEN
            SET v_score = v_score - 10;
        END IF;
    END IF;
    
    -- 确保评分在0-100范围内
    IF v_score < 0 THEN
        SET v_score = 0;
    END IF;
    
    SET p_health_score = v_score;
END //
DELIMITER ;

-- =============================================
-- 数据分区 - 提高大数据量查询性能
-- =============================================

-- 按年份分区体检报告表（MySQL 8.0+）
-- ALTER TABLE health_reports 
-- PARTITION BY RANGE (YEAR(report_date)) (
--     PARTITION p2020 VALUES LESS THAN (2021),
--     PARTITION p2021 VALUES LESS THAN (2022),
--     PARTITION p2022 VALUES LESS THAN (2023),
--     PARTITION p2023 VALUES LESS THAN (2024),
--     PARTITION p2024 VALUES LESS THAN (2025),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- =============================================
-- 查询优化示例
-- =============================================

-- 优化前的查询（避免使用）
-- SELECT * FROM health_reports WHERE user_id = 'user123' ORDER BY report_date DESC;

-- 优化后的查询
-- SELECT id, report_date, hospital, health_score 
-- FROM health_reports 
-- WHERE user_id = 'user123' 
-- ORDER BY report_date DESC 
-- LIMIT 10;

-- 使用索引的复合查询
-- SELECT hr.*, bt.glucose, bt.cholesterol
-- FROM health_reports hr
-- JOIN blood_tests bt ON hr.id = bt.report_id
-- WHERE hr.user_id = 'user123' 
-- AND hr.report_date >= '2023-01-01'
-- ORDER BY hr.report_date DESC;

-- =============================================
-- 数据库维护脚本
-- =============================================

-- 分析表统计信息
ANALYZE TABLE users, health_reports, blood_tests, liver_function_tests, kidney_function_tests, ecg_tests;

-- 优化表
OPTIMIZE TABLE users, health_reports, blood_tests, liver_function_tests, kidney_function_tests, ecg_tests;

-- 检查表完整性
CHECK TABLE users, health_reports, blood_tests, liver_function_tests, kidney_function_tests, ecg_tests;

-- =============================================
-- 性能监控查询
-- =============================================

-- 查看慢查询
-- SHOW VARIABLES LIKE 'slow_query_log';
-- SHOW VARIABLES LIKE 'long_query_time';

-- 查看索引使用情况
-- SHOW INDEX FROM health_reports;

-- 查看表大小
-- SELECT 
--     table_name AS 'Table',
--     ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
-- FROM information_schema.TABLES 
-- WHERE table_schema = 'health_app'
-- ORDER BY (data_length + index_length) DESC;
