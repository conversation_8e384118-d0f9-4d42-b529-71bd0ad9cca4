<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="体检应用 - 您的健康管理助手">
  <meta name="theme-color" content="#4285f4">
  <title>体检应用 - 趋势分析</title>
  <link rel="stylesheet" href="css/styles.css">
  <link rel="stylesheet" href="css/mobile.css">
  <link rel="stylesheet" href="css/charts.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <link rel="icon" href="images/favicon.ico" type="image/x-icon">
  <style>
    .indicator-selector {
      margin-bottom: 20px;
    }
    
    .indicator-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: 10px;
      margin-bottom: 20px;
    }
    
    .indicator-option {
      padding: 10px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s;
    }
    
    .indicator-option.active {
      background-color: var(--primary-color);
      color: white;
      border-color: var(--primary-color);
    }
    
    .trend-filters {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-bottom: 20px;
    }
    
    .trend-filter {
      flex: 1;
      min-width: 120px;
    }
    
    .trend-filter-label {
      display: block;
      margin-bottom: 5px;
      font-size: 0.9rem;
      color: var(--text-secondary);
    }
    
    .trend-filter-select {
      width: 100%;
      padding: 8px;
      border: 1px solid var(--border-color);
      border-radius: 6px;
      background-color: var(--card-background);
      color: var(--text-primary);
    }
    
    .trend-analysis {
      margin-top: 20px;
      padding: 15px;
      background-color: rgba(66, 133, 244, 0.1);
      border-radius: 8px;
    }
    
    .trend-analysis h3 {
      margin-bottom: 10px;
    }
  </style>
</head>
<body>
  <!-- 头部 -->
  <header class="app-header">
    <div class="app-title">趋势分析</div>
    <div class="user-info">
      <img id="user-avatar" class="avatar" src="images/default-avatar.png" alt="用户头像">
    </div>
  </header>

  <!-- 主内容 -->
  <div class="container">
    <div class="card">
      <h2>指标趋势分析</h2>
      
      <!-- 指标选择器 -->
      <div class="indicator-selector">
        <h3>选择指标</h3>
        <div class="indicator-grid">
          <div class="indicator-option active" data-indicator="cholesterol">总胆固醇</div>
          <div class="indicator-option" data-indicator="glucose">血糖</div>
          <div class="indicator-option" data-indicator="ldl">低密度脂蛋白</div>
          <div class="indicator-option" data-indicator="hdl">高密度脂蛋白</div>
          <div class="indicator-option" data-indicator="alt">丙氨酸转氨酶</div>
          <div class="indicator-option" data-indicator="cre">肌酐</div>
        </div>
      </div>
      
      <!-- 趋势过滤器 -->
      <div class="trend-filters">
        <div class="trend-filter">
          <label class="trend-filter-label">时间范围</label>
          <select class="trend-filter-select" id="time-range">
            <option value="3m">最近3个月</option>
            <option value="6m">最近6个月</option>
            <option value="1y" selected>最近1年</option>
            <option value="all">全部时间</option>
          </select>
        </div>
        <div class="trend-filter">
          <label class="trend-filter-label">显示方式</label>
          <select class="trend-filter-select" id="chart-type">
            <option value="line">折线图</option>
            <option value="bar">柱状图</option>
          </select>
        </div>
      </div>
      
      <!-- 趋势图表 -->
      <div class="chart-container">
        <canvas id="trend-chart"></canvas>
      </div>
      
      <!-- 趋势分析 -->
      <div class="trend-analysis">
        <h3>趋势分析</h3>
        <p>您的总胆固醇水平在过去一年中呈现波动上升趋势，最近一次检测值为5.8 mmol/L，超出正常范围（0-5.2 mmol/L）。建议您注意饮食控制，增加运动量，并定期复查。</p>
      </div>
    </div>
    
    <!-- 参考范围卡片 -->
    <div class="card">
      <h2>参考范围</h2>
      <table class="indicators-table">
        <thead>
          <tr>
            <th>指标名称</th>
            <th>参考范围</th>
            <th>单位</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>总胆固醇</td>
            <td>0-5.2</td>
            <td>mmol/L</td>
          </tr>
          <tr>
            <td>低密度脂蛋白</td>
            <td>0-3.4</td>
            <td>mmol/L</td>
          </tr>
          <tr>
            <td>高密度脂蛋白</td>
            <td>≥1.0</td>
            <td>mmol/L</td>
          </tr>
          <tr>
            <td>血糖</td>
            <td>3.9-6.1</td>
            <td>mmol/L</td>
          </tr>
          <tr>
            <td>丙氨酸转氨酶</td>
            <td>0-40</td>
            <td>U/L</td>
          </tr>
          <tr>
            <td>肌酐</td>
            <td>44-133</td>
            <td>μmol/L</td>
          </tr>
        </tbody>
      </table>
    </div>
    
    <!-- 健康建议卡片 -->
    <div class="card">
      <h2>健康建议</h2>
      <div class="health-tips">
        <div class="health-tip">
          <div class="health-tip-icon"><i class="fas fa-heartbeat"></i></div>
          <div class="health-tip-content">
            <h3>控制血脂</h3>
            <p>根据您的总胆固醇趋势，建议减少高脂肪食物摄入，增加运动量，每周至少进行150分钟中等强度有氧运动。</p>
          </div>
        </div>
        <div class="health-tip">
          <div class="health-tip-icon"><i class="fas fa-calendar-check"></i></div>
          <div class="health-tip-content">
            <h3>定期复查</h3>
            <p>建议您每3个月复查一次血脂，监测指标变化趋势。</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 底部导航 -->
  <nav class="bottom-nav">
    <a href="index.html" class="nav-item">
      <i class="fas fa-home nav-icon"></i>
      <span class="nav-text">首页</span>
    </a>
    <a href="reports.html" class="nav-item">
      <i class="fas fa-clipboard-list nav-icon"></i>
      <span class="nav-text">报告</span>
    </a>
    <a href="trends.html" class="nav-item active">
      <i class="fas fa-chart-line nav-icon"></i>
      <span class="nav-text">趋势</span>
    </a>
    <a href="compare.html" class="nav-item">
      <i class="fas fa-balance-scale nav-icon"></i>
      <span class="nav-text">对比</span>
    </a>
  </nav>

  <!-- 加载Chart.js库 -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="js/utils/api.js"></script>
  <script>
    // 血糖趋势数据
    const ctx = document.getElementById('trend-chart').getContext('2d');
    new Chart(ctx, {
      type: 'line',
      data: {
        labels: ['三月', '四月', '五月'],
        datasets: [{
          label: '总胆固醇 (mmol/L)',
          data: [5.2, 5.8, 5.5],
          borderColor: '#4285f4',
          backgroundColor: 'rgba(52, 168, 83, 0.1)',
          tension: 0.4,
          fill: true,
          pointRadius: 5,
          pointHoverRadius: 7
        }]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            min: 3,
            max: 7,
            ticks: {
              stepSize: 0.5
            }
          }
        }
      }
    });
  </script>
  <script src="js/utils/storage.js"></script>
  <script src="js/components/charts.js"></script>
  <script src="js/pages/trends.js"></script>
</body>
</html>
