<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="体检应用 - 您的健康管理助手">
  <meta name="theme-color" content="#4285f4">
  <title>体检应用 - 数据对比</title>
  <link rel="stylesheet" href="css/styles.css">
  <link rel="stylesheet" href="css/mobile.css">
  <link rel="stylesheet" href="css/charts.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <link rel="icon" href="images/favicon.ico" type="image/x-icon">
  <style>
    .compare-tabs {
      display: flex;
      border-bottom: 1px solid var(--border-color);
      margin-bottom: 20px;
    }
    
    .compare-tab {
      padding: 10px 15px;
      cursor: pointer;
      color: var(--text-secondary);
      border-bottom: 2px solid transparent;
    }
    
    .compare-tab.active {
      color: var(--primary-color);
      border-bottom-color: var(--primary-color);
    }
    
    .compare-options {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-bottom: 20px;
    }
    
    .compare-option-group {
      flex: 1;
      min-width: 150px;
    }
    
    .compare-option-label {
      display: block;
      margin-bottom: 5px;
      font-size: 0.9rem;
      color: var(--text-secondary);
    }
    
    .compare-option-select {
      width: 100%;
      padding: 10px;
      border: 1px solid var(--border-color);
      border-radius: 6px;
      background-color: var(--card-background);
      color: var(--text-primary);
    }
    
    .ranking-list {
      list-style: none;
      margin-top: 20px;
    }
    
    .ranking-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid var(--border-color);
    }
    
    .ranking-position {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background-color: var(--primary-color);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      margin-right: 15px;
    }
    
    .ranking-position.top-1 {
      background-color: gold;
      color: black;
    }
    
    .ranking-position.top-2 {
      background-color: silver;
      color: black;
    }
    
    .ranking-position.top-3 {
      background-color: #cd7f32; /* bronze */
      color: white;
    }
    
    .ranking-details {
      flex: 1;
    }
    
    .ranking-name {
      font-weight: 500;
    }
    
    .ranking-value {
      color: var(--text-secondary);
      font-size: 0.9rem;
    }
    
    .ranking-bar {
      height: 6px;
      background-color: var(--border-color);
      border-radius: 3px;
      margin-top: 5px;
      overflow: hidden;
    }
    
    .ranking-progress {
      height: 100%;
      background-color: var(--primary-color);
    }
    
    .your-position {
      background-color: rgba(66, 133, 244, 0.1);
      border-left: 3px solid var(--primary-color);
    }
  </style>
</head>
<body>
  <!-- 头部 -->
  <header class="app-header">
    <div class="app-title">数据对比</div>
    <div class="user-info">
      <img id="user-avatar" class="avatar" src="images/default-avatar.png" alt="用户头像">
    </div>
  </header>

  <!-- 主内容 -->
  <div class="container">
    <div class="card">
      <h2>健康数据对比</h2>
      
      <!-- 对比类型标签页 -->
      <div class="compare-tabs">
        <div class="compare-tab active" data-tab="demographic">人群对比</div>
        <div class="compare-tab" data-tab="personal">个人历史对比</div>
      </div>
      
      <!-- 对比选项 -->
      <div class="compare-options">
        <div class="compare-option-group">
          <label class="compare-option-label">年龄段</label>
          <select class="compare-option-select" id="age-group">
            <option value="18-30">18-30岁</option>
            <option value="31-40" selected>31-40岁</option>
            <option value="41-50">41-50岁</option>
            <option value="51-60">51-60岁</option>
            <option value="60+">60岁以上</option>
          </select>
        </div>
        <div class="compare-option-group">
          <label class="compare-option-label">性别</label>
          <select class="compare-option-select" id="gender">
            <option value="male" selected>男性</option>
            <option value="female">女性</option>
            <option value="all">全部</option>
          </select>
        </div>
        <div class="compare-option-group">
          <label class="compare-option-label">指标</label>
          <select class="compare-option-select" id="indicator">
            <option value="health-score" selected>健康评分</option>
            <option value="cholesterol">总胆固醇</option>
            <option value="glucose">血糖</option>
            <option value="bmi">体质指数(BMI)</option>
          </select>
        </div>
      </div>
      
      <!-- 对比图表 -->
      <div class="chart-container">
        <canvas id="compare-chart"></canvas>
      </div>
      
      <!-- 对比分析 -->
      <div class="trend-analysis">
        <h3>对比分析</h3>
        <p>您的健康评分为85分，处于31-40岁男性人群的前30%，高于平均水平（78分）。您的总体健康状况良好，但仍有改进空间。</p>
      </div>
    </div>
    
    <!-- 排名卡片 -->
    <div class="card">
      <h2>健康排名</h2>
      <p>您在同龄人群中的健康排名情况：</p>
      
      <ul class="ranking-list">
        <li class="ranking-item">
          <div class="ranking-position top-1">1</div>
          <div class="ranking-details">
            <div class="ranking-name">优秀</div>
            <div class="ranking-value">90-100分 (前10%)</div>
            <div class="ranking-bar">
              <div class="ranking-progress" style="width: 10%"></div>
            </div>
          </div>
        </li>
        <li class="ranking-item">
          <div class="ranking-position top-2">2</div>
          <div class="ranking-details">
            <div class="ranking-name">良好</div>
            <div class="ranking-value">80-89分 (前30%)</div>
            <div class="ranking-bar">
              <div class="ranking-progress" style="width: 20%"></div>
            </div>
          </div>
        </li>
        <li class="ranking-item your-position">
          <div class="ranking-position">3</div>
          <div class="ranking-details">
            <div class="ranking-name">一般 (您的位置)</div>
            <div class="ranking-value">70-79分 (中等)</div>
            <div class="ranking-bar">
              <div class="ranking-progress" style="width: 40%"></div>
            </div>
          </div>
        </li>
        <li class="ranking-item">
          <div class="ranking-position">4</div>
          <div class="ranking-details">
            <div class="ranking-name">较差</div>
            <div class="ranking-value">60-69分 (后30%)</div>
            <div class="ranking-bar">
              <div class="ranking-progress" style="width: 20%"></div>
            </div>
          </div>
        </li>
        <li class="ranking-item">
          <div class="ranking-position">5</div>
          <div class="ranking-details">
            <div class="ranking-name">需注意</div>
            <div class="ranking-value">60分以下 (后10%)</div>
            <div class="ranking-bar">
              <div class="ranking-progress" style="width: 10%"></div>
            </div>
          </div>
        </li>
      </ul>
    </div>
    
    <!-- 改进建议卡片 -->
    <div class="card">
      <h2>改进建议</h2>
      <div class="health-tips">
        <div class="health-tip">
          <div class="health-tip-icon"><i class="fas fa-heartbeat"></i></div>
          <div class="health-tip-content">
            <h3>控制血脂</h3>
            <p>您的总胆固醇水平高于同龄人群平均水平。建议减少高脂肪食物摄入，增加运动量。</p>
          </div>
        </div>
        <div class="health-tip">
          <div class="health-tip-icon"><i class="fas fa-apple-alt"></i></div>
          <div class="health-tip-content">
            <h3>健康饮食</h3>
            <p>增加蔬果摄入，减少精制碳水化合物，控制总热量。</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 底部导航 -->
  <nav class="bottom-nav">
    <a href="index.html" class="nav-item">
      <i class="fas fa-home nav-icon"></i>
      <span class="nav-text">首页</span>
    </a>
    <a href="reports.html" class="nav-item">
      <i class="fas fa-clipboard-list nav-icon"></i>
      <span class="nav-text">报告</span>
    </a>
    <a href="trends.html" class="nav-item">
      <i class="fas fa-chart-line nav-icon"></i>
      <span class="nav-text">趋势</span>
    </a>
    <a href="compare.html" class="nav-item active">
      <i class="fas fa-balance-scale nav-icon"></i>
      <span class="nav-text">对比</span>
    </a>
  </nav>

  <!-- 加载Chart.js库 -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="js/components/charts.js"></script>
  <!-- 在Chart.js引用后添加 -->
  <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-crosshair@1.2"></script>
  
  <!-- 在现有脚本区域添加 -->
  <script>
  document.addEventListener('DOMContentLoaded', () => {
    // ... existing code ...
    
    // 生成模拟分布数据（实际应从API获取）
    const distributionData = Array.from({length: 101}, (_,i) => 
    Math.exp(-Math.pow(i-75,2)/800)*30 // 调整后的分布曲线
    );
    
    // 初始化图表时传入三个参数
    charts.createDistributionCurveChart('compare-chart', 85, distributionData);
    
    // 添加实时数据追踪
    Chart.defaults.plugins.tooltip.callbacks.label = function(context) {
      return `分数: ${context.parsed.x}  占比: ${context.parsed.y}%`;
    };
  });
  </script>
  <script src="js/utils/api.js"></script>
  <script src="js/utils/storage.js"></script>
  
  <script src="js/pages/compare.js"></script>
</body>
</html>
