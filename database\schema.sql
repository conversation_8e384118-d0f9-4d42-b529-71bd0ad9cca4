-- 用户表
CREATE TABLE IF NOT EXISTS users (
  id VARCHAR(50) PRIMARY KEY,
  name VA<PERSON>HA<PERSON>(100) NOT NULL,
  email VARCHAR(100),
  avatar_url VARCHAR(255),
  gender VARCHAR(10),
  birth_date DATE,
  height FLOAT,
  weight FLOAT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 基本信息表 - 体检报告主表
CREATE TABLE IF NOT EXISTS health_reports (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(50) NOT NULL,
  report_date DATE NOT NULL,
  hospital VARCHAR(100),
  doctor <PERSON><PERSON><PERSON><PERSON>(100),
  health_score INT,
  report_summary TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

--心血管表
CREATE TABLE IF NOT EXISTS cardiovascular_tests (
  id VARCHAR(36) PRIMARY KEY,
  report_id VARCHAR(36) NOT NULL,
  CM01 VARCHAR(10), -- 心电图
  IM01 VARCHAR(10), -- 心脏CTA
  CM02 VARCHAR(10), -- 乳酸脱氢酶
  CM03 VARCHAR(10), -- 肌酸激酶
  CM04 VARCHAR(10), -- 肌酸激酶同工酶
  FOREIGN KEY (report_id) REFERENCES health_reports(id)
);

--肾功能表
CREATE TABLE IF NOT EXISTS kidney_function_tests (
  id VARCHAR(36) PRIMARY KEY,
  report_id VARCHAR(36) NOT NULL,
  RF01 VARCHAR(10), -- 尿素
  RF02 VARCHAR(10), -- 肌酐
  RF03 VARCHAR(10), -- 尿酸
  RF04 VARCHAR(10), -- 尿素氮
  RF05 VARCHAR(10), -- 胱抑素C
  FOREIGN KEY (report_id) REFERENCES health_reports(id)
);

--肝功能表
CREATE TABLE IF NOT EXISTS liver_function_tests (
  id VARCHAR(36) PRIMARY KEY,
  report_id VARCHAR(36) NOT NULL,
  LF01 VARCHAR(10), -- 总胆红素
  LF02 VARCHAR(10), -- 直接胆红素
  LF03 VARCHAR(10), -- 间接胆红素
  LF04 VARCHAR(10), -- 总蛋白
  LF05 VARCHAR(10), -- 白蛋白
  LF06 VARCHAR(10), -- 球蛋白
  LF07 VARCHAR(10), -- 白/球
  LF08 VARCHAR(10), -- 丙氨酸氨基转移酶
  LF09 VARCHAR(10), -- 天门冬氨酸氨基转移酶
  LF10 VARCHAR(10), -- 碱性磷酸酶
  LF11 VARCHAR(10), -- γ-谷氨酰基转移酶
  LF12 VARCHAR(10), -- 谷氨酰转肽酶
  LF13 VARCHAR(10), -- 谷丙转氨酶
  LF14 VARCHAR(10), -- 总胆汁酸
  LF15 VARCHAR(10), -- 肝纤维化
  LF16 VARCHAR(10), -- 透明质酸
  LF17 VARCHAR(10), -- IV胶原
  LF18 VARCHAR(10), -- 层粘蛋白
  LF19 VARCHAR(10), -- III型前胶原氨基端肽
  FOREIGN KEY (report_id) REFERENCES health_reports(id)
);

--高血压表
CREATE TABLE IF NOT EXISTS hypertension_tests (
  id VARCHAR(36) PRIMARY KEY,
  report_id VARCHAR(36) NOT NULL,
  HT01 VARCHAR(10), -- 收缩压
  HT02 VARCHAR(10), -- 舒张压
  FOREIGN KEY (report_id) REFERENCES health_reports(id)
);

--高血脂表
CREATE TABLE IF NOT EXISTS hyperlipidemia_tests (
  id VARCHAR(36) PRIMARY KEY,
  report_id VARCHAR(36) NOT NULL,
  HL01 VARCHAR(10), -- 总胆固醇
  HL02 VARCHAR(10), -- 甘油三脂
  HL03 VARCHAR(10), -- 甘油三酯(去游离)
  HL04 VARCHAR(10), -- 高密度脂蛋白
  HL05 VARCHAR(10), -- *低密度脂蛋白
  HL06 VARCHAR(10), -- 低密度脂蛋白
  HL07 VARCHAR(10), -- 高密度脂蛋白胆固醇
  HL08 VARCHAR(10), -- 低密度脂蛋白胆固醇
  HL09 VARCHAR(10), -- 载脂蛋白A
  HL10 VARCHAR(10), -- 载脂蛋白B
  HL11 VARCHAR(10), -- 脂蛋白(a)
  HL12 VARCHAR(10), -- 小而密低密度脂蛋白胆固醇
  FOREIGN KEY (report_id) REFERENCES health_reports(id)
);

--超声表
CREATE TABLE IF NOT EXISTS ultrasound_tests (
  id VARCHAR(36) PRIMARY KEY,
  report_id VARCHAR(36) NOT NULL,
  IM02 VARCHAR(10), -- 胰肝胆脾肾
  IM03 VARCHAR(10), -- 肝胆脾肾
  IM04 VARCHAR(10), -- 肝胆脾肾（空腹）
  TH01 VARCHAR(10), -- 甲状腺B超
  IM05 VARCHAR(10), -- 前列腺B超
  IM06 VARCHAR(10), -- 颈部血管彩超
  IM07 VARCHAR(10), -- 膀胱、输尿管B超
  IM08 VARCHAR(10), -- 子宫、附件超声(经阴道)
  IM09 VARCHAR(10), -- 乳腺B超
  FOREIGN KEY (report_id) REFERENCES health_reports(id)
);

--健康评分表
CREATE TABLE IF NOT EXISTS health_scores (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(50) NOT NULL,
  score INT NOT NULL,
  score_date DATE NOT NULL,
  score_label VARCHAR(20), -- 优秀/良好/一般/较差/需注意
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

--健康建议表
CREATE TABLE IF NOT EXISTS health_recommendations (
  id VARCHAR(36) PRIMARY KEY,
  report_id VARCHAR(36) NOT NULL,
  category VARCHAR(50), -- 饮食/运动/生活方式等
  title VARCHAR(100) NOT NULL,
  content TEXT NOT NULL,
  priority INT DEFAULT 0, -- 建议优先级
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (report_id) REFERENCES health_reports(id)
);

--人群统计数据表
CREATE TABLE IF NOT EXISTS demographic_stats (
  id VARCHAR(36) PRIMARY KEY,
  age_group VARCHAR(20) NOT NULL, -- 18-30/31-40/41-50/51-60/60+
  gender VARCHAR(10) NOT NULL, -- male/female/all
  indicator_name VARCHAR(50) NOT NULL,
  avg_value FLOAT NOT NULL,
  min_value FLOAT,
  max_value FLOAT,
  percentile_10 FLOAT,
  percentile_25 FLOAT,
  percentile_50 FLOAT,
  percentile_75 FLOAT,
  percentile_90 FLOAT,
  sample_size INT,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户表
CREATE TABLE IF NOT EXISTS users (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  email VARCHAR(100),
  avatar_url VARCHAR(255),
  gender VARCHAR(10),
  birth_date DATE,
  height FLOAT,
  weight FLOAT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 基本信息表 - 体检报告主表
CREATE TABLE IF NOT EXISTS health_reports (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(50) NOT NULL,
  report_date DATE NOT NULL,
  hospital VARCHAR(100),
  doctor VARCHAR(100),
  health_score INT,
  report_summary TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

--心血管表
CREATE TABLE IF NOT EXISTS cardiovascular_tests (
  id VARCHAR(36) PRIMARY KEY,
  report_id VARCHAR(36) NOT NULL,
  CM01 VARCHAR(10), -- 心电图
  IM01 VARCHAR(10), -- 心脏CTA
  CM02 VARCHAR(10), -- 乳酸脱氢酶
  CM03 VARCHAR(10), -- 肌酸激酶
  CM04 VARCHAR(10), -- 肌酸激酶同工酶
  FOREIGN KEY (report_id) REFERENCES health_reports(id)
);

--肾功能表
CREATE TABLE IF NOT EXISTS kidney_function_tests (
  id VARCHAR(36) PRIMARY KEY,
  report_id VARCHAR(36) NOT NULL,
  RF01 VARCHAR(10), -- 尿素
  RF02 VARCHAR(10), -- 肌酐
  RF03 VARCHAR(10), -- 尿酸
  RF04 VARCHAR(10), -- 尿素氮
  RF05 VARCHAR(10), -- 胱抑素C
  FOREIGN KEY (report_id) REFERENCES health_reports(id)
);

--肝功能表
CREATE TABLE IF NOT EXISTS liver_function_tests (
  id VARCHAR(36) PRIMARY KEY,
  report_id VARCHAR(36) NOT NULL,
  LF01 VARCHAR(10), -- 总胆红素
  LF02 VARCHAR(10), -- 直接胆红素
  LF03 VARCHAR(10), -- 间接胆红素
  LF04 VARCHAR(10), -- 总蛋白
  LF05 VARCHAR(10), -- 白蛋白
  LF06 VARCHAR(10), -- 球蛋白
  LF07 VARCHAR(10), -- 白/球
  LF08 VARCHAR(10), -- 丙氨酸氨基转移酶
  LF09 VARCHAR(10), -- 天门冬氨酸氨基转移酶
  LF10 VARCHAR(10), -- 碱性磷酸酶
  LF11 VARCHAR(10), -- γ-谷氨酰基转移酶
  LF12 VARCHAR(10), -- 谷氨酰转肽酶
  LF13 VARCHAR(10), -- 谷丙转氨酶
  LF14 VARCHAR(10), -- 总胆汁酸
  LF15 VARCHAR(10), -- 肝纤维化
  LF16 VARCHAR(10), -- 透明质酸
  LF17 VARCHAR(10), -- IV胶原
  LF18 VARCHAR(10), -- 层粘蛋白
  LF19 VARCHAR(10), -- III型前胶原氨基端肽
  FOREIGN KEY (report_id) REFERENCES health_reports(id)
);

--高血压表
CREATE TABLE IF NOT EXISTS hypertension_tests (
  id VARCHAR(36) PRIMARY KEY,
  report_id VARCHAR(36) NOT NULL,
  HT01 VARCHAR(10), -- 收缩压
  HT02 VARCHAR(10), -- 舒张压
  FOREIGN KEY (report_id) REFERENCES health_reports(id)
);

--高血脂表
CREATE TABLE IF NOT EXISTS hyperlipidemia_tests (
  id VARCHAR(36) PRIMARY KEY,
  report_id VARCHAR(36) NOT NULL,
  HL01 VARCHAR(10), -- 总胆固醇
  HL02 VARCHAR(10), -- 甘油三脂
  HL03 VARCHAR(10), -- 甘油三酯(去游离)
  HL04 VARCHAR(10), -- 高密度脂蛋白
  HL05 VARCHAR(10), -- *低密度脂蛋白
  HL06 VARCHAR(10), -- 低密度脂蛋白
  HL07 VARCHAR(10), -- 高密度脂蛋白胆固醇
  HL08 VARCHAR(10), -- 低密度脂蛋白胆固醇
  HL09 VARCHAR(10), -- 载脂蛋白A
  HL10 VARCHAR(10), -- 载脂蛋白B
  HL11 VARCHAR(10), -- 脂蛋白(a)
  HL12 VARCHAR(10), -- 小而密低密度脂蛋白胆固醇
  FOREIGN KEY (report_id) REFERENCES health_reports(id)
);

--超声表
CREATE TABLE IF NOT EXISTS ultrasound_tests (
  id VARCHAR(36) PRIMARY KEY,
  report_id VARCHAR(36) NOT NULL,
  IM02 VARCHAR(10), -- 胰肝胆脾肾
  IM03 VARCHAR(10), -- 肝胆脾肾
  IM04 VARCHAR(10), -- 肝胆脾肾（空腹）
  TH01 VARCHAR(10), -- 甲状腺B超
  IM05 VARCHAR(10), -- 前列腺B超
  IM06 VARCHAR(10), -- 颈部血管彩超
  IM07 VARCHAR(10), -- 膀胱、输尿管B超
  IM08 VARCHAR(10), -- 子宫、附件超声(经阴道)
  IM09 VARCHAR(10), -- 乳腺B超
  FOREIGN KEY (report_id) REFERENCES health_reports(id)
);

--健康评分表
CREATE TABLE IF NOT EXISTS health_scores (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(50) NOT NULL,
  score INT NOT NULL,
  score_date DATE NOT NULL,
  score_label VARCHAR(20), -- 优秀/良好/一般/较差/需注意
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

--健康建议表
CREATE TABLE IF NOT EXISTS health_recommendations (
  id VARCHAR(36) PRIMARY KEY,
  report_id VARCHAR(36) NOT NULL,
  category VARCHAR(50), -- 饮食/运动/生活方式等
  title VARCHAR(100) NOT NULL,
  content TEXT NOT NULL,
  priority INT DEFAULT 0, -- 建议优先级
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (report_id) REFERENCES health_reports(id)
);

--人群统计数据表
CREATE TABLE IF NOT EXISTS demographic_stats (
  id VARCHAR(36) PRIMARY KEY,
  age_group VARCHAR(20) NOT NULL, -- 18-30/31-40/41-50/51-60/60+
  gender VARCHAR(10) NOT NULL, -- male/female/all
  indicator_name VARCHAR(50) NOT NULL,
  avg_value FLOAT NOT NULL,
  min_value FLOAT,
  max_value FLOAT,
  percentile_10 FLOAT,
  percentile_25 FLOAT,
  percentile_50 FLOAT,
  percentile_75 FLOAT,
  percentile_90 FLOAT,
  sample_size INT,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户表
CREATE TABLE IF NOT EXISTS users (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  email VARCHAR(100),
  avatar_url VARCHAR(255),
  gender VARCHAR(10),
  birth_date DATE,
  height FLOAT,
  weight FLOAT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 基本信息表 - 体检报告主表
CREATE TABLE IF NOT EXISTS health_reports (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(50) NOT NULL,
  report_date DATE NOT NULL,
  hospital VARCHAR(100),
  doctor VARCHAR(100),
  health_score INT,
  report_summary TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

--心血管表
CREATE TABLE IF NOT EXISTS cardiovascular_tests (
  id VARCHAR(36) PRIMARY KEY,
  report_id VARCHAR(36) NOT NULL,
  CM01 VARCHAR(10), -- 心电图
  IM01 VARCHAR(10), -- 心脏CTA
  CM02 VARCHAR(10), -- 乳酸脱氢酶
  CM03 VARCHAR(10), -- 肌酸激酶
  CM04 VARCHAR(10), -- 肌酸激酶同工酶
  FOREIGN KEY (report_id) REFERENCES health_reports(id)
);

--肾功能表
CREATE TABLE IF NOT EXISTS kidney_function_tests (
  id VARCHAR(36) PRIMARY KEY,
  report_id VARCHAR(36) NOT NULL,
  RF01 VARCHAR(10), -- 尿素
  RF02 VARCHAR(10), -- 肌酐
  RF03 VARCHAR(10), -- 尿酸
  RF04 VARCHAR(10), -- 尿素氮
  RF05 VARCHAR(10), -- 胱抑素C
  FOREIGN KEY (report_id) REFERENCES health_reports(id)
);

--肝功能表
CREATE TABLE IF NOT EXISTS liver_function_tests (
  id VARCHAR(36) PRIMARY KEY,
  report_id VARCHAR(36) NOT NULL,
  LF01 VARCHAR(10), -- 总胆红素
  LF02 VARCHAR(10), -- 直接胆红素
  LF03 VARCHAR(10), -- 间接胆红素
  LF04 VARCHAR(10), -- 总蛋白
  LF05 VARCHAR(10), -- 白蛋白
  LF06 VARCHAR(10), -- 球蛋白
  LF07 VARCHAR(10), -- 白/球
  LF08 VARCHAR(10), -- 丙氨酸氨基转移酶
  LF09 VARCHAR(10), -- 天门冬氨酸氨基转移酶
  LF10 VARCHAR(10), -- 碱性磷酸酶
  LF11 VARCHAR(10), -- γ-谷氨酰基转移酶
  LF12 VARCHAR(10), -- 谷氨酰转肽酶
  LF13 VARCHAR(10), -- 谷丙转氨酶
  LF14 VARCHAR(10), -- 总胆汁酸
  LF15 VARCHAR(10), -- 肝纤维化
  LF16 VARCHAR(10), -- 透明质酸
  LF17 VARCHAR(10), -- IV胶原
  LF18 VARCHAR(10), -- 层粘蛋白
  LF19 VARCHAR(10), -- III型前胶原氨基端肽
  FOREIGN KEY (report_id) REFERENCES health_reports(id)
);

--高血压表
CREATE TABLE IF NOT EXISTS hypertension_tests (
  id VARCHAR(36) PRIMARY KEY,
  report_id VARCHAR(36) NOT NULL,
  HT01 VARCHAR(10), -- 收缩压
  HT02 VARCHAR(10), -- 舒张压
  FOREIGN KEY (report_id) REFERENCES health_reports(id)
);

--高血脂表
CREATE TABLE IF NOT EXISTS hyperlipidemia_tests (
  id VARCHAR(36) PRIMARY KEY,
  report_id VARCHAR(36) NOT NULL,
  HL01 VARCHAR(10), -- 总胆固醇
  HL02 VARCHAR(10), -- 甘油三脂
  HL03 VARCHAR(10), -- 甘油三酯(去游离)
  HL04 VARCHAR(10), -- 高密度脂蛋白
  HL05 VARCHAR(10), -- *低密度脂蛋白
  HL06 VARCHAR(10), -- 低密度脂蛋白
  HL07 VARCHAR(10), -- 高密度脂蛋白胆固醇
  HL08 VARCHAR(10), -- 低密度脂蛋白胆固醇
  HL09 VARCHAR(10), -- 载脂蛋白A
  HL10 VARCHAR(10), -- 载脂蛋白B
  HL11 VARCHAR(10), -- 脂蛋白(a)
  HL12 VARCHAR(10), -- 小而密低密度脂蛋白胆固醇
  FOREIGN KEY (report_id) REFERENCES health_reports(id)
);

--超声表
CREATE TABLE IF NOT EXISTS ultrasound_tests (
  id VARCHAR(36) PRIMARY KEY,
  report_id VARCHAR(36) NOT NULL,
  IM02 VARCHAR(10), -- 胰肝胆脾肾
  IM03 VARCHAR(10), -- 肝胆脾肾
  IM04 VARCHAR(10), -- 肝胆脾肾（空腹）
  TH01 VARCHAR(10), -- 甲状腺B超
  IM05 VARCHAR(10), -- 前列腺B超
  IM06 VARCHAR(10), -- 颈部血管彩超
  IM07 VARCHAR(10), -- 膀胱、输尿管B超
  IM08 VARCHAR(10), -- 子宫、附件超声(经阴道)
  IM09 VARCHAR(10), -- 乳腺B超
  FOREIGN KEY (report_id) REFERENCES health_reports(id)
);

--健康评分表
CREATE TABLE IF NOT EXISTS health_scores (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(50) NOT NULL,
  score INT NOT NULL,
  score_date DATE NOT NULL,
  score_label VARCHAR(20), -- 优秀/良好/一般/较差/需注意
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

--健康建议表
CREATE TABLE IF NOT EXISTS health_recommendations (
  id VARCHAR(36) PRIMARY KEY,
  report_id VARCHAR(36) NOT NULL,
  category VARCHAR(50), -- 饮食/运动/生活方式等
  title VARCHAR(100) NOT NULL,
  content TEXT NOT NULL,
  priority INT DEFAULT 0, -- 建议优先级
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (report_id) REFERENCES health_reports(id)
);

--人群统计数据表
CREATE TABLE IF NOT EXISTS demographic_stats (
  id VARCHAR(36) PRIMARY KEY,
  age_group VARCHAR(20) NOT NULL, -- 18-30/31-40/41-50/51-60/60+
  gender VARCHAR(10) NOT NULL, -- male/female/all
  indicator_name VARCHAR(50) NOT NULL,
  avg_value FLOAT NOT NULL,
  min_value FLOAT,
  max_value FLOAT,
  percentile_10 FLOAT,
  percentile_25 FLOAT,
  percentile_50 FLOAT,
  percentile_75 FLOAT,
  percentile_90 FLOAT,
  sample_size INT,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 人群统计数据表
CREATE TABLE IF NOT EXISTS demographic_stats (
  id VARCHAR(36) PRIMARY KEY,
  age_group VARCHAR(20) NOT NULL, -- 18-30/31-40/41-50/51-60/60+
  gender VARCHAR(10) NOT NULL, -- male/female/all
  indicator_name VARCHAR(50) NOT NULL,
  avg_value FLOAT NOT NULL,
  min_value FLOAT,
  max_value FLOAT,
  percentile_10 FLOAT,
  percentile_25 FLOAT,
  percentile_50 FLOAT,
  percentile_75 FLOAT,
  percentile_90 FLOAT,
  sample_size INT,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入映射数据
CREATE TABLE IF NOT EXISTS field_mappings (
  original_name VARCHAR(100) NOT NULL, -- 原列名
  field_name VARCHAR(50) NOT NULL, -- 新列名
  category VARCHAR(10) NOT NULL, -- 类别
  PRIMARY KEY (original_name, field_name)
);

-- 插入映射数据
INSERT INTO field_mappings (original_name, field_name, category) VALUES
('报告日期', 'report_date', 'BS'),
('性别', 'sex', 'BS'),
('年龄', 'age', 'BS'),
('身高', 'BS01', 'BS'),
('体重', 'BS02', 'BS'),
('收缩压', 'HT01', 'HT'),
('舒张压', 'HT02', 'HT'),
('白细胞', 'BC01', 'BC'),
('单核细胞百分数', 'BC02', 'BC'),
('单核细胞绝对值', 'BC03', 'BC'),
('红细胞分布宽度', 'BC04', 'BC'),
('红细胞计数', 'BC05', 'BC'),
('红细胞压积', 'BC06', 'BC'),
('淋巴细胞百分数', 'BC07', 'BC'),
('淋巴细胞绝对值', 'BC08', 'BC'),
('平均红细胞体积', 'BC09', 'BC'),
('平均血红蛋白量', 'BC10', 'BC'),
('平均血红蛋白浓度', 'BC11', 'BC'),
('平均血小板体积', 'BC12', 'BC'),
('嗜碱粒细胞百分数', 'BC13', 'BC'),
('嗜碱粒细胞绝对值', 'BC14', 'BC'),
('嗜酸粒细胞百分数', 'BC15', 'BC'),
('嗜酸粒细胞绝对值', 'BC16', 'BC'),
('血红蛋白', 'BC17', 'BC'),
('血小板分布宽度', 'BC18', 'BC'),
('血小板计数', 'BC19', 'BC'),
('血小板压积', 'BC20', 'BC'),
('中性粒细胞百分数', 'BC21', 'BC'),
('中性粒细胞绝对值', 'BC22', 'BC'),
('白细胞(镜检)', 'UC01', 'UC'),
('比重', 'UC02', 'UC'),
('红细胞(镜检)', 'UC03', 'UC'),
('颗粒管型(镜检)', 'UC04', 'UC'),
('尿白细胞', 'UC05', 'UC'),
('尿胆红素', 'UC06', 'UC'),
('尿胆原', 'UC07', 'UC'),
('尿蛋白', 'UC08', 'UC'),
('尿酸碱度', 'UC09', 'UC'),
('尿糖', 'UC10', 'UC'),
('尿酮体', 'UC11', 'UC'),
('尿亚硝酸盐', 'UC12', 'UC'),
('尿隐血', 'UC13', 'UC'),
('脓细胞(镜检)', 'UC14', 'UC'),
('上皮细胞(镜检)', 'UC15', 'UC'),
('透明管型(镜检)', 'UC16', 'UC'),
('维生素C', 'UC17', 'UC'),
('线状粘液(镜检)', 'UC18', 'UC'),
('心电图', 'CM01', 'CM'),
('心脏CTA', 'IM01', 'IM'),
('碳13呼气实验', 'OT01', 'OT'),
('总胆红素', 'LF01', 'LF'),
('直接胆红素', 'LF02', 'LF'),
('间接胆红素', 'LF03', 'LF'),
('总蛋白', 'LF04', 'LF'),
('白蛋白', 'LF05', 'LF'),
('球蛋白', 'LF06', 'LF'),
('白/球', 'LF07', 'LF'),
('丙氨酸氨基转移酶', 'LF08', 'LF'),
('天门冬氨酸氨基转移酶', 'LF09', 'LF'),
('碱性磷酸酶', 'LF10', 'LF'),
('γ-谷氨酰基转移酶', 'LF11', 'LF'),
('谷氨酰转肽酶', 'LF12', 'LF'),
('谷丙转氨酶', 'LF13', 'LF'),
('乳酸脱氢酶', 'CM02', 'CM'),
('肌酸激酶', 'CM03', 'CM'),
('尿素', 'RF01', 'RF'),
('肌酐', 'RF02', 'RF'),
('尿酸', 'RF03', 'RF'),
('尿素氮', 'RF04', 'RF'),
('血糖', 'BC23', 'BC'),
('总胆固醇', 'HL01', 'HL'),
('甘油三脂', 'HL02', 'HL'),
('甘油三酯(去游离)', 'HL03', 'HL'),
('高密度脂蛋白', 'HL04', 'HL'),
('*低密度脂蛋白', 'HL05', 'HL'),
('低密度脂蛋白', 'HL06', 'HL'),
('高密度脂蛋白胆固醇', 'HL07', 'HL'),
('低密度脂蛋白胆固醇', 'HL08', 'HL'),
('载脂蛋白A', 'HL09', 'HL'),
('载脂蛋白B', 'HL10', 'HL'),
('C反应蛋白', 'II01', 'II'),
('总胆汁酸', 'LF14', 'LF'),
('胱抑素C', 'RF05', 'RF'),
('肌酸激酶同工酶', 'CM04', 'CM'),
('脂蛋白(a)', 'HL11', 'HL'),
('抗O', 'II02', 'II'),
('类风湿因子', 'II03', 'II'),
('钾', 'OT02', 'OT'),
('钠', 'OT03', 'OT'),
('氯', 'OT04', 'OT'),
('钙', 'OT05', 'OT'),
('果糖胺', 'OT06', 'OT'),
('纤维连接蛋白', 'OT07', 'OT'),
('触珠蛋白', 'OT08', 'OT'),
('α-1酸性糖蛋白', 'OT09', 'OT'),
('小而密低密度脂蛋白胆固醇', 'HL12', 'HL'),
('胰肝胆脾肾', 'IM02', 'IM'),
('肝胆脾肾', 'IM03', 'IM'),
('肝胆脾肾（空腹）', 'IM04', 'IM'),
('甲状腺B超', 'TH01', 'TH'),
('前列腺B超', 'IM05', 'IM'),
('颈部血管彩超', 'IM06', 'IM'),
('膀胱、输尿管B超', 'IM07', 'IM'),
('子宫、附件超声(经阴道)', 'IM08', 'IM'),
('乳腺B超', 'IM09', 'IM'),
('肝纤维化', 'LF15', 'LF'),
('透明质酸', 'LF16', 'LF'),
('IV胶原', 'LF17', 'LF'),
('层粘蛋白', 'LF18', 'LF'),
('III型前胶原氨基端肽', 'LF19', 'LF');

