/**
 * 性能优化工具模块
 * 包含各种性能优化技术的实现
 */

const Performance = {
  /**
   * 初始化性能监控
   */
  init() {
    this.setupPerformanceObserver();
    this.setupResourceHints();
    this.setupLazyLoading();
    this.setupServiceWorker();
    this.measurePageLoad();
  },

  /**
   * 设置性能观察器
   */
  setupPerformanceObserver() {
    if ('PerformanceObserver' in window) {
      // 监控关键性能指标
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'largest-contentful-paint') {
            console.log('LCP:', entry.startTime);
            this.reportMetric('LCP', entry.startTime);
          }
          
          if (entry.entryType === 'first-input') {
            console.log('FID:', entry.processingStart - entry.startTime);
            this.reportMetric('FID', entry.processingStart - entry.startTime);
          }
          
          if (entry.entryType === 'layout-shift') {
            if (!entry.hadRecentInput) {
              console.log('CLS:', entry.value);
              this.reportMetric('CLS', entry.value);
            }
          }
        }
      });

      // 观察不同类型的性能条目
      observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });
    }
  },

  /**
   * 设置资源提示
   */
  setupResourceHints() {
    // 预加载关键资源
    const criticalResources = [
      { href: '/css/styles.css', as: 'style' },
      { href: '/js/app.js', as: 'script' },
      { href: 'https://cdn.jsdelivr.net/npm/chart.js', as: 'script' }
    ];

    criticalResources.forEach(resource => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = resource.href;
      link.as = resource.as;
      document.head.appendChild(link);
    });

    // DNS预解析
    const domains = [
      'api.github.com',
      'cdn.jsdelivr.net',
      'cdnjs.cloudflare.com'
    ];

    domains.forEach(domain => {
      const link = document.createElement('link');
      link.rel = 'dns-prefetch';
      link.href = `//${domain}`;
      document.head.appendChild(link);
    });
  },

  /**
   * 设置图片懒加载
   */
  setupLazyLoading() {
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            img.src = img.dataset.src;
            img.classList.remove('lazy');
            observer.unobserve(img);
          }
        });
      });

      // 观察所有懒加载图片
      document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
      });
    } else {
      // 降级方案：立即加载所有图片
      document.querySelectorAll('img[data-src]').forEach(img => {
        img.src = img.dataset.src;
      });
    }
  },

  /**
   * 注册Service Worker
   */
  setupServiceWorker() {
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then(registration => {
            console.log('SW registered: ', registration);
          })
          .catch(registrationError => {
            console.log('SW registration failed: ', registrationError);
          });
      });
    }
  },

  /**
   * 测量页面加载性能
   */
  measurePageLoad() {
    window.addEventListener('load', () => {
      // 使用Performance API测量加载时间
      const perfData = performance.getEntriesByType('navigation')[0];
      
      const metrics = {
        dns: perfData.domainLookupEnd - perfData.domainLookupStart,
        tcp: perfData.connectEnd - perfData.connectStart,
        ttfb: perfData.responseStart - perfData.requestStart,
        download: perfData.responseEnd - perfData.responseStart,
        domReady: perfData.domContentLoadedEventEnd - perfData.navigationStart,
        loadComplete: perfData.loadEventEnd - perfData.navigationStart
      };

      console.log('Page Load Metrics:', metrics);
      this.reportMetrics(metrics);
    });
  },

  /**
   * 防抖函数
   * @param {Function} func - 要防抖的函数
   * @param {number} wait - 等待时间
   * @returns {Function} 防抖后的函数
   */
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  /**
   * 节流函数
   * @param {Function} func - 要节流的函数
   * @param {number} limit - 限制时间
   * @returns {Function} 节流后的函数
   */
  throttle(func, limit) {
    let inThrottle;
    return function(...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },

  /**
   * 虚拟滚动实现
   * @param {HTMLElement} container - 容器元素
   * @param {Array} items - 数据项
   * @param {Function} renderItem - 渲染函数
   * @param {number} itemHeight - 项目高度
   */
  virtualScroll(container, items, renderItem, itemHeight) {
    const containerHeight = container.clientHeight;
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    const totalHeight = items.length * itemHeight;
    
    let scrollTop = 0;
    let startIndex = 0;
    let endIndex = Math.min(startIndex + visibleCount, items.length);

    // 创建虚拟容器
    const virtualContainer = document.createElement('div');
    virtualContainer.style.height = `${totalHeight}px`;
    virtualContainer.style.position = 'relative';

    // 创建可见项容器
    const visibleContainer = document.createElement('div');
    visibleContainer.style.position = 'absolute';
    visibleContainer.style.top = '0';
    visibleContainer.style.width = '100%';

    virtualContainer.appendChild(visibleContainer);
    container.appendChild(virtualContainer);

    // 渲染可见项
    const renderVisibleItems = () => {
      visibleContainer.innerHTML = '';
      visibleContainer.style.transform = `translateY(${startIndex * itemHeight}px)`;

      for (let i = startIndex; i < endIndex; i++) {
        const item = renderItem(items[i], i);
        item.style.height = `${itemHeight}px`;
        visibleContainer.appendChild(item);
      }
    };

    // 滚动事件处理
    const handleScroll = this.throttle(() => {
      scrollTop = container.scrollTop;
      startIndex = Math.floor(scrollTop / itemHeight);
      endIndex = Math.min(startIndex + visibleCount + 1, items.length);
      renderVisibleItems();
    }, 16);

    container.addEventListener('scroll', handleScroll);
    renderVisibleItems();
  },

  /**
   * 图片压缩
   * @param {File} file - 图片文件
   * @param {number} quality - 压缩质量 (0-1)
   * @param {number} maxWidth - 最大宽度
   * @returns {Promise<Blob>} 压缩后的图片
   */
  compressImage(file, quality = 0.8, maxWidth = 1920) {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // 计算新尺寸
        const ratio = Math.min(maxWidth / img.width, maxWidth / img.height);
        canvas.width = img.width * ratio;
        canvas.height = img.height * ratio;

        // 绘制压缩图片
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

        // 转换为Blob
        canvas.toBlob(resolve, 'image/jpeg', quality);
      };

      img.src = URL.createObjectURL(file);
    });
  },

  /**
   * 预加载图片
   * @param {Array} urls - 图片URL数组
   * @returns {Promise} 预加载完成的Promise
   */
  preloadImages(urls) {
    const promises = urls.map(url => {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = resolve;
        img.onerror = reject;
        img.src = url;
      });
    });

    return Promise.all(promises);
  },

  /**
   * 内存使用监控
   */
  monitorMemoryUsage() {
    if ('memory' in performance) {
      const memInfo = performance.memory;
      console.log('Memory Usage:', {
        used: Math.round(memInfo.usedJSHeapSize / 1048576) + ' MB',
        total: Math.round(memInfo.totalJSHeapSize / 1048576) + ' MB',
        limit: Math.round(memInfo.jsHeapSizeLimit / 1048576) + ' MB'
      });
    }
  },

  /**
   * 报告性能指标
   * @param {string} name - 指标名称
   * @param {number} value - 指标值
   */
  reportMetric(name, value) {
    // 这里可以发送到分析服务
    if (typeof gtag !== 'undefined') {
      gtag('event', 'performance_metric', {
        metric_name: name,
        metric_value: Math.round(value),
        custom_parameter: 'health_app'
      });
    }
  },

  /**
   * 报告多个性能指标
   * @param {Object} metrics - 指标对象
   */
  reportMetrics(metrics) {
    Object.entries(metrics).forEach(([name, value]) => {
      this.reportMetric(name, value);
    });
  }
};

// 自动初始化性能监控
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => Performance.init());
} else {
  Performance.init();
}
