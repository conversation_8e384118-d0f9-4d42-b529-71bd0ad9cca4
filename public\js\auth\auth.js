/**
 * 认证相关JavaScript
 * 处理用户登录、认证状态管理和错误处理
 * <AUTHOR>
 * @version 1.1.0
 */

/**
 * 认证模块
 */
const Auth = {
  /**
   * 初始化认证模块
   */
  init() {
    this.setupEventListeners();
    this.checkAuthStatus();
    this.checkErrorParams();
    this.setupAutoLogout();
  },

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // GitHub登录按钮
    const githubLoginBtn = document.getElementById('github-login');
    if (githubLoginBtn) {
      githubLoginBtn.addEventListener('click', this.handleGithubLogin.bind(this));
    }

    // 手机号登录按钮
    const phoneLoginBtn = document.getElementById('phone-login');
    if (phoneLoginBtn) {
      phoneLoginBtn.addEventListener('click', this.handlePhoneLogin.bind(this));
    }

    // 邮箱登录按钮
    const emailLoginBtn = document.getElementById('email-login');
    if (emailLoginBtn) {
      emailLoginBtn.addEventListener('click', this.handleEmailLogin.bind(this));
    }

    // 错误对话框关闭按钮
    const errorCloseBtn = document.getElementById('error-close');
    if (errorCloseBtn) {
      errorCloseBtn.addEventListener('click', () => {
        const errorDialog = document.getElementById('error-dialog');
        if (errorDialog) {
          errorDialog.close();
        }
      });
    }
  },

  /**
   * 处理GitHub登录
   * @param {Event} event - 点击事件
   */
  handleGithubLogin(event) {
    event.preventDefault();

    // 检查网络连接
    if (!this.checkNetworkConnection()) {
      this.showError('无法连接到网络，请检查您的网络连接后重试。');
      return;
    }

    // 显示加载对话框
    this.showLoading('正在准备登录...');

    // 保存当前URL，以便登录后返回
    this.saveReturnUrl();

    // 短暂延迟以显示加载动画
    setTimeout(() => {
      try {
        // 生成并保存CSRF状态
        const state = this.generateState();
        localStorage.setItem('oauth_state', state);
        localStorage.setItem('oauth_state_expires', Date.now() + 10 * 60 * 1000); // 10分钟过期

        // 重定向到服务器的GitHub OAuth路由
        window.location.href = `/auth/github?state=${state}`;
      } catch (error) {
        this.hideLoading();
        this.showError('启动登录过程时出错: ' + error.message);
        console.error('GitHub登录错误:', error);
      }
    }, 500);
  },

  /**
   * 处理手机号登录
   * @param {Event} event - 点击事件
   */
  handlePhoneLogin(event) {
    event.preventDefault();

    // 创建模态框
    this.createPhoneLoginModal();
  },

  /**
   * 创建手机号登录模态框
   */
  createPhoneLoginModal() {
    // 检查是否已存在模态框
    let modal = document.getElementById('phone-login-modal');
    if (modal) {
      modal.classList.add('active');
      return;
    }

    // 创建模态框
    modal = document.createElement('div');
    modal.id = 'phone-login-modal';
    modal.className = 'modal';

    // 模态框内容
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">手机号登录</h3>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <p>手机号登录功能即将推出，敬请期待！</p>
          <div class="form-group">
            <label class="form-label">手机号</label>
            <input type="tel" class="form-input" placeholder="请输入手机号" disabled>
          </div>
          <div class="form-group">
            <label class="form-label">验证码</label>
            <div style="display: flex; gap: 10px;">
              <input type="text" class="form-input" placeholder="请输入验证码" style="flex: 1;" disabled>
              <button class="btn btn-primary" disabled>获取验证码</button>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary modal-close">关闭</button>
          <button class="btn btn-primary" disabled>登录</button>
        </div>
      </div>
    `;

    // 添加到文档
    document.body.appendChild(modal);

    // 显示模态框
    setTimeout(() => {
      modal.classList.add('active');
    }, 10);

    // 关闭按钮事件
    const closeButtons = modal.querySelectorAll('.modal-close');
    closeButtons.forEach(button => {
      button.addEventListener('click', () => {
        modal.classList.remove('active');
        setTimeout(() => {
          modal.remove();
        }, 300);
      });
    });
  },

  /**
   * 处理邮箱登录
   * @param {Event} event - 点击事件
   */
  handleEmailLogin(event) {
    event.preventDefault();

    // 创建模态框
    this.createEmailLoginModal();
  },

  /**
   * 创建邮箱登录模态框
   */
  createEmailLoginModal() {
    // 检查是否已存在模态框
    let modal = document.getElementById('email-login-modal');
    if (modal) {
      modal.classList.add('active');
      return;
    }

    // 创建模态框
    modal = document.createElement('div');
    modal.id = 'email-login-modal';
    modal.className = 'modal';

    // 模态框内容
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">邮箱登录</h3>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <p>邮箱登录功能即将推出，敬请期待！</p>
          <div class="form-group">
            <label class="form-label">邮箱地址</label>
            <input type="email" class="form-input" placeholder="请输入邮箱地址" disabled>
          </div>
          <div class="form-group">
            <label class="form-label">密码</label>
            <input type="password" class="form-input" placeholder="请输入密码" disabled>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary modal-close">关闭</button>
          <button class="btn btn-primary" disabled>登录</button>
        </div>
      </div>
    `;

    // 添加到文档
    document.body.appendChild(modal);

    // 显示模态框
    setTimeout(() => {
      modal.classList.add('active');
    }, 10);

    // 关闭按钮事件
    const closeButtons = modal.querySelectorAll('.modal-close');
    closeButtons.forEach(button => {
      button.addEventListener('click', () => {
        modal.classList.remove('active');
        setTimeout(() => {
          modal.remove();
        }, 300);
      });
    });
  },

  /**
   * 检查URL参数中的错误信息和其他参数
   */
  checkErrorParams() {
    const urlParams = new URLSearchParams(window.location.search);
    const error = urlParams.get('error');
    const logout = urlParams.get('logout');

    // 处理错误参数
    if (error) {
      let errorMessage = '登录失败';

      switch (error) {
        case 'invalid_state':
          errorMessage = '登录会话已过期或无效，请重试。';
          break;
        case 'github_oauth_error':
          errorMessage = 'GitHub登录过程中出现错误，请稍后重试。';
          break;
        case 'invalid_token':
          errorMessage = '登录令牌无效，请重新登录。';
          break;
        case 'session_expired':
          errorMessage = '登录会话已过期，请重新登录。';
          break;
        case 'no_token':
          errorMessage = '未获取到登录令牌，请重新登录。';
          break;
        case 'server_error':
          errorMessage = '服务器错误，请稍后重试。';
          break;
        default:
          errorMessage = '登录过程中出现未知错误，请重试。';
      }

      this.showError(errorMessage);

      // 清除URL参数
      const url = new URL(window.location.href);
      url.searchParams.delete('error');
      window.history.replaceState({}, document.title, url.toString());
    }

    // 处理登出参数
    if (logout === 'true') {
      // 显示登出成功消息
      this.showLogoutSuccess();

      // 清除URL参数
      const url = new URL(window.location.href);
      url.searchParams.delete('logout');
      window.history.replaceState({}, document.title, url.toString());
    }
  },

  /**
   * 显示登出成功消息
   */
  showLogoutSuccess() {
    // 创建一个临时通知
    const notification = document.createElement('div');
    notification.className = 'logout-notification';
    notification.innerHTML = `
      <div class="logout-notification-content">
        <i class="fas fa-check-circle"></i>
        <span>您已成功登出</span>
      </div>
    `;

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
      .logout-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: var(--secondary-color);
        color: white;
        padding: 12px 20px;
        border-radius: 6px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        z-index: 1000;
        opacity: 0;
        transform: translateY(-20px);
        transition: all 0.3s ease;
      }
      .logout-notification.active {
        opacity: 1;
        transform: translateY(0);
      }
      .logout-notification-content {
        display: flex;
        align-items: center;
        gap: 10px;
      }
      .logout-notification i {
        font-size: 1.2rem;
      }
    `;

    // 添加到文档
    document.head.appendChild(style);
    document.body.appendChild(notification);

    // 显示通知
    setTimeout(() => {
      notification.classList.add('active');
    }, 10);

    // 3秒后隐藏通知
    setTimeout(() => {
      notification.classList.remove('active');
      setTimeout(() => {
        notification.remove();
      }, 300);
    }, 3000);
  },

  /**
   * 检查认证状态
   */
  checkAuthStatus() {
    // 检查是否已登录
    const token = localStorage.getItem('token');

    if (token) {
      // 验证令牌有效性
      fetch('/api/auth/verify', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        if (data.valid) {
          // 令牌有效，检查当前页面是否为登录页
          if (window.location.pathname.includes('login.html')) {
            // 获取返回URL
            const returnUrl = this.getReturnUrl() || '/index.html';
            // 重定向到返回URL
            window.location.href = returnUrl;
          }
        } else {
          // 令牌无效，清除存储
          this.logout(false);
        }
      })
      .catch(error => {
        console.error('验证令牌时出错:', error);
        // 出错时也清除存储
        this.logout(false);
      });
    } else {
      // 未登录，检查当前页面是否需要认证
      if (!window.location.pathname.includes('login.html') &&
          !window.location.pathname.includes('callback.html')) {
        // 保存当前URL
        this.saveReturnUrl(window.location.href);
        // 重定向到登录页
        window.location.href = '/login.html';
      }
    }
  },

  /**
   * 设置自动登出
   * 当令牌过期时自动登出
   */
  setupAutoLogout() {
    const token = localStorage.getItem('token');
    if (!token) return;

    try {
      // 解析JWT令牌
      const payload = JSON.parse(atob(token.split('.')[1]));
      const expiryTime = payload.exp * 1000; // 转换为毫秒
      const currentTime = Date.now();

      if (expiryTime <= currentTime) {
        // 令牌已过期
        this.logout(false);
        this.showError('登录会话已过期，请重新登录。');
        return;
      }

      // 设置定时器，在令牌过期前5分钟提醒用户
      const timeToExpiry = expiryTime - currentTime;
      const reminderTime = timeToExpiry - (5 * 60 * 1000); // 5分钟前提醒

      if (reminderTime > 0) {
        setTimeout(() => {
          this.showSessionExpiryWarning(Math.floor(timeToExpiry / 60000));
        }, reminderTime);
      }

      // 设置定时器，在令牌过期时自动登出
      setTimeout(() => {
        this.logout(true);
      }, timeToExpiry);
    } catch (error) {
      console.error('解析令牌时出错:', error);
    }
  },

  /**
   * 显示会话即将过期警告
   * @param {number} minutesLeft - 剩余分钟数
   */
  showSessionExpiryWarning(minutesLeft) {
    // 创建警告模态框
    let modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = 'session-expiry-warning';

    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">会话即将过期</h3>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <p>您的登录会话将在约 ${minutesLeft} 分钟后过期。是否继续保持登录状态？</p>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary modal-close">忽略</button>
          <button class="btn btn-primary" id="extend-session-btn">继续登录</button>
        </div>
      </div>
    `;

    // 添加到文档
    document.body.appendChild(modal);

    // 显示模态框
    setTimeout(() => {
      modal.classList.add('active');
    }, 10);

    // 关闭按钮事件
    const closeButtons = modal.querySelectorAll('.modal-close');
    closeButtons.forEach(button => {
      button.addEventListener('click', () => {
        modal.classList.remove('active');
        setTimeout(() => {
          modal.remove();
        }, 300);
      });
    });

    // 继续登录按钮事件
    const extendBtn = document.getElementById('extend-session-btn');
    if (extendBtn) {
      extendBtn.addEventListener('click', () => {
        // 刷新令牌
        this.refreshToken();
        // 关闭模态框
        modal.classList.remove('active');
        setTimeout(() => {
          modal.remove();
        }, 300);
      });
    }
  },

  /**
   * 刷新认证令牌
   */
  refreshToken() {
    const token = localStorage.getItem('token');
    if (!token) return;

    this.showLoading('正在刷新登录状态...');

    fetch('/api/auth/refresh', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      if (data.token) {
        // 更新令牌
        localStorage.setItem('token', data.token);
        // 更新用户信息
        if (data.user) {
          localStorage.setItem('user', JSON.stringify(data.user));
        }
        // 重新设置自动登出
        this.setupAutoLogout();
        this.hideLoading();
      } else {
        throw new Error('未获取到新令牌');
      }
    })
    .catch(error => {
      console.error('刷新令牌时出错:', error);
      this.hideLoading();
      this.showError('刷新登录状态失败，请重新登录。');
      this.logout(false);
    });
  },

  /**
   * 登出
   * @param {boolean} redirect - 是否重定向到登录页
   */
  logout(redirect = true) {
    const token = localStorage.getItem('token');

    // 如果有令牌，调用登出API
    if (token) {
      // 尝试通知服务器登出
      fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      }).catch(error => {
        console.error('登出API调用失败:', error);
        // 即使API调用失败，也继续本地登出流程
      }).finally(() => {
        // 无论API调用成功与否，都执行本地登出
        this.performLocalLogout(redirect);
      });
    } else {
      // 没有令牌，直接执行本地登出
      this.performLocalLogout(redirect);
    }
  },

  /**
   * 执行本地登出
   * @param {boolean} redirect - 是否重定向到登录页
   */
  performLocalLogout(redirect = true) {
    // 清除本地存储
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('oauth_state');
    localStorage.removeItem('oauth_state_expires');

    // 清除所有与认证相关的缓存
    const keys = Object.keys(localStorage);
    for (const key of keys) {
      if (key.startsWith('health_app_')) {
        localStorage.removeItem(key);
      }
    }

    // 重定向到登录页
    if (redirect && !window.location.pathname.includes('login.html')) {
      window.location.href = '/login.html?logout=true';
    }
  },

  /**
   * 生成随机状态
   * @returns {string} 随机状态字符串
   */
  generateState() {
    const array = new Uint8Array(24);
    window.crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  },

  /**
   * 保存返回URL
   * @param {string} url - 返回URL，默认为当前URL
   */
  saveReturnUrl(url = null) {
    if (!url) {
      // 如果当前是登录页，不保存
      if (window.location.pathname.includes('login.html')) {
        return;
      }
      url = window.location.href;
    }
    localStorage.setItem('auth_return_url', url);
  },

  /**
   * 获取返回URL
   * @returns {string|null} 返回URL
   */
  getReturnUrl() {
    const url = localStorage.getItem('auth_return_url');
    // 获取后清除
    localStorage.removeItem('auth_return_url');
    return url;
  },

  /**
   * 显示错误对话框
   * @param {string} message - 错误信息
   */
  showError(message) {
    const errorDialog = document.getElementById('error-dialog');
    const errorMessage = document.getElementById('error-message');

    if (!errorDialog || !errorMessage) {
      alert(message);
      return;
    }

    errorMessage.textContent = message;
    errorDialog.showModal();
  },

  /**
   * 显示加载对话框
   * @param {string} message - 加载信息
   */
  showLoading(message) {
    const loadingDialog = document.getElementById('loading-dialog');
    const loadingMessage = document.getElementById('loading-message');

    if (!loadingDialog || !loadingMessage) {
      return;
    }

    loadingMessage.textContent = message;
    loadingDialog.showModal();
  },

  /**
   * 隐藏加载对话框
   */
  hideLoading() {
    const loadingDialog = document.getElementById('loading-dialog');

    if (loadingDialog) {
      loadingDialog.close();
    }
  },

  /**
   * 检查网络连接
   * @returns {boolean} 是否有网络连接
   */
  checkNetworkConnection() {
    return navigator.onLine;
  }
};

// 在DOM加载完成后初始化认证模块
document.addEventListener('DOMContentLoaded', () => {
  Auth.init();

  // 处理网络状态变化
  window.addEventListener('online', () => {
    console.log('网络已连接');
  });

  window.addEventListener('offline', () => {
    console.log('网络已断开');
    Auth.showError('网络连接已断开，请检查您的网络设置。');
  });
});
