<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="体检应用 - 您的健康管理助手">
  <meta name="theme-color" content="#4285f4">
  <title>体检应用 - 登录</title>
  <link rel="stylesheet" href="css/styles.css">
  <link rel="stylesheet" href="css/mobile.css">
  <link rel="icon" href="images/favicon.ico" type="image/x-icon">
  <style>
    body {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
    }
    
    .login-card {
      background-color: var(--card-background);
      border-radius: 12px;
      padding: 30px;
      box-shadow: 0 10px 25px var(--shadow-color);
      text-align: center;
      width: 100%;
      max-width: 400px;
    }
    
    .app-logo {
      width: 80px;
      height: 80px;
      margin-bottom: 15px;
    }
    
    .login-btn {
      width: 100%;
      padding: 12px;
      border: none;
      border-radius: 6px;
      background-color: #24292e;
      color: white;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      transition: all 0.3s ease;
    }
    
    .login-btn:hover {
      background-color: #2c3238;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    .login-btn svg {
      fill: white;
    }
    
    .login-options {
      margin-top: 30px;
      border-top: 1px solid var(--border-color);
      padding-top: 20px;
    }
    
    .options-buttons {
      display: flex;
      gap: 10px;
    }
    
    .option-btn {
      flex: 1;
      padding: 10px;
      border: 1px solid var(--border-color);
      border-radius: 6px;
      background-color: var(--card-background);
      color: var(--text-primary);
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 5px;
    }
    
    .terms-privacy {
      margin-top: 20px;
      font-size: 12px;
      color: var(--text-secondary);
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="login-card">
      <img src="images/logo.png" alt="体检应用" class="app-logo">
      <h1>体检应用</h1>
      <p>您的健康管理助手</p>
      
      <button id="github-login" class="login-btn">
        <svg viewBox="0 0 24 24" width="24" height="24">
          <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
        </svg>
        使用 GitHub 账号登录
      </button>
      
      <div class="login-options">
        <p>其他登录方式</p>
        <div class="options-buttons">
          <button id="phone-login" class="option-btn">
            <svg viewBox="0 0 24 24" width="16" height="16">
              <path d="M17,19H7V5H17M17,1H7C5.89,1 5,1.89 5,3V21A2,2 0 0,0 7,23H17A2,2 0 0,0 19,21V3C19,1.89 18.1,1 17,1Z" />
            </svg>
            手机号
          </button>
          <button id="email-login" class="option-btn">
            <svg viewBox="0 0 24 24" width="16" height="16">
              <path d="M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z" />
            </svg>
            邮箱
          </button>
        </div>
      </div>
      
      <div class="terms-privacy">
        <small>登录即表示您同意我们的<a href="/terms.html">服务条款</a>和<a href="/privacy.html">隐私政策</a></small>
      </div>
    </div>
  </div>

  <dialog id="loading-dialog">
    <div class="loading-spinner"></div>
    <p id="loading-message">正在登录，请稍候...</p>
  </dialog>

  <dialog id="error-dialog">
    <div class="error-icon">!</div>
    <h3>登录失败</h3>
    <p id="error-message"></p>
    <button id="error-close" class="dialog-btn">关闭</button>
  </dialog>

  <script src="js/auth/auth.js"></script>
</body>
</html>
