# 移动端体检应用文件结构

```
/health-check-app
│
├── /public                  # 静态资源目录
│   ├── /css                 # 样式文件
│   │   ├── styles.css       # 主样式文件
│   │   ├── charts.css       # 图表样式
│   │   └── mobile.css       # 移动端响应式样式
│   │
│   ├── /js                  # JavaScript文件
│   │   ├── /auth            # 认证相关
│   │   │   ├── auth.js      # 认证逻辑
│   │   │   └── callback.js  # OAuth回调处理
│   │   │
│   │   ├── /components      # 组件
│   │   │   ├── header.js    # 头部组件
│   │   │   ├── footer.js    # 底部组件
│   │   │   ├── charts.js    # 图表组件
│   │   │   └── timeline.js  # 时间轴组件
│   │   │
│   │   ├── /pages           # 页面逻辑
│   │   │   ├── home.js      # 首页
│   │   │   ├── reports.js   # 报告列表
│   │   │   ├── detail.js    # 报告详情
│   │   │   ├── trends.js    # 趋势分析
│   │   │   └── compare.js   # 数据对比
│   │   │
│   │   ├── /utils           # 工具函数
│   │   │   ├── api.js       # API请求
│   │   │   ├── storage.js   # 本地存储
│   │   │   └── helpers.js   # 辅助函数
│   │   │
│   │   └── app.js           # 主应用逻辑
│   │
│   ├── /images              # 图片资源
│   │
│   ├── /lib                 # 第三方库
│   │   ├── chart.min.js     # Chart.js图表库
│   │   └── ...
│   │
│   ├── index.html           # 首页
│   ├── login.html           # 登录页
│   ├── callback.html        # OAuth回调页
│   ├── reports.html         # 报告列表页
│   ├── report-detail.html   # 报告详情页
│   ├── trends.html          # 趋势分析页
│   └── compare.html         # 数据对比页
│
├── /server                  # 服务器端代码
│   ├── /controllers         # 控制器
│   │   ├── authController.js    # 认证控制器
│   │   ├── reportController.js  # 报告控制器
│   │   ├── userController.js    # 用户控制器
│   │   └── statsController.js   # 统计控制器
│   │
│   ├── /models              # 数据模型
│   │   ├── User.js          # 用户模型
│   │   ├── Report.js        # 报告模型
│   │   ├── BloodTest.js     # 血液检查模型
│   │   ├── LiverTest.js     # 肝功能检查模型
│   │   ├── KidneyTest.js    # 肾功能检查模型
│   │   ├── EcgTest.js       # 心电图检查模型
│   │   └── FieldMapping.js  # 字段映射模型
│   │
│   ├── /routes              # 路由
│   │   ├── authRoutes.js    # 认证路由
│   │   ├── reportRoutes.js  # 报告路由
│   │   ├── userRoutes.js    # 用户路由
│   │   └── statsRoutes.js   # 统计路由
│   │
│   ├── /utils               # 工具函数
│   │   ├── database.js      # 数据库连接
│   │   ├── auth.js          # 认证工具
│   │   └── helpers.js       # 辅助函数
│   │
│   ├── /middleware          # 中间件
│   │   ├── auth.js          # 认证中间件
│   │   └── errorHandler.js  # 错误处理中间件
│   │
│   └── server.js            # 服务器入口文件
│
├── /database                # 数据库相关
│   ├── schema.sql           # 数据库模式
│   └── seed.js              # 种子数据
│
├── /config                  # 配置文件
│   ├── default.js           # 默认配置
│   └── production.js        # 生产环境配置
│
├── package.json             # 项目依赖
├── .env                     # 环境变量
└── README.md                # 项目说明
```
